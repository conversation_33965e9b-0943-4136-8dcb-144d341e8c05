package com.tecnodrive.analytics.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Driver Behavior Analysis DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DriverBehaviorAnalysis {
    private String driverId;
    private double overallRating;
    private List<String> behaviorPatterns;
    private List<String> improvementSuggestions;
    private String riskLevel;
    private LocalDateTime analysisDate;
    private double safetyScore;
}
