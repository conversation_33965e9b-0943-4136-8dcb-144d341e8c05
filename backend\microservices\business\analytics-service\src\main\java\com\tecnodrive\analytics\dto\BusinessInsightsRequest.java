package com.tecnodrive.analytics.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Business Insights Request DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BusinessInsightsRequest {
    private String startDate;
    private String endDate;
    private String businessUnit;
    private String[] metrics;
    private boolean includeForecasting;
}
