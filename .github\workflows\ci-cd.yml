name: TECNO DRIVE CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Backend Tests
  backend-tests:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v4
    
    - name: Set up JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'
    
    - name: Cache Maven dependencies
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2
    
    - name: Run Backend Tests
      run: |
        cd backend
        mvn clean test -Dspring.profiles.active=test
    
    - name: Generate Test Report
      uses: dorny/test-reporter@v1
      if: success() || failure()
      with:
        name: Backend Test Results
        path: backend/**/target/surefire-reports/*.xml
        reporter: java-junit

  # Frontend Tests
  frontend-tests:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        app: [admin-dashboard, shared-components]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/${{ matrix.app }}/package-lock.json
    
    - name: Install dependencies
      run: |
        cd frontend/${{ matrix.app }}
        npm ci
    
    - name: Run tests
      run: |
        cd frontend/${{ matrix.app }}
        npm test -- --coverage --watchAll=false
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: frontend/${{ matrix.app }}/coverage/lcov.info
        flags: frontend-${{ matrix.app }}

  # Security Scanning
  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

  # Build and Push Docker Images
  build-and-push:
    needs: [backend-tests, frontend-tests, security-scan]
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    strategy:
      matrix:
        service:
          - auth-service
          - user-service
          - payment-service
          - ride-service
          - fleet-service
          - location-service
          - analytics-service
          - notification-service
          - parcel-service
          - api-gateway
          - eureka-server
          - config-server
          - monitoring-service
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/${{ matrix.service }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}
    
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: backend/microservices/*/${{ matrix.service }}
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # Deploy to Staging
  deploy-staging:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Deploy to Staging
      run: |
        echo "Deploying to staging environment..."
        # Add deployment scripts here
        # kubectl apply -f k8s/staging/
    
    - name: Run Integration Tests
      run: |
        echo "Running integration tests..."
        # Add integration test scripts here
    
    - name: Notify Deployment Status
      if: always()
      run: |
        echo "Deployment to staging completed with status: ${{ job.status }}"
        echo "Timestamp: $(date)"

  # Deploy to Production
  deploy-production:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Deploy to Production
      run: |
        echo "Deploying to production environment..."
        # Add production deployment scripts here
        # kubectl apply -f k8s/production/
    
    - name: Run Smoke Tests
      run: |
        echo "Running smoke tests..."
        # Add smoke test scripts here
    
    - name: Notify Production Status
      if: always()
      run: |
        echo "Production deployment completed with status: ${{ job.status }}"
        echo "Timestamp: $(date)"

  # Performance Tests
  performance-tests:
    needs: deploy-staging
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Run Load Tests
      run: |
        echo "Running performance tests..."
        # Add load testing scripts here (e.g., k6, JMeter)
    
    - name: Generate Performance Report
      run: |
        echo "Generating performance report..."
        # Generate and upload performance reports
