#!/bin/bash

# TECNO DRIVE System Test Script
# Tests the core functionality of implemented services

echo "🚗 TECNO DRIVE - System Test Script"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Base URLs
API_GATEWAY="http://localhost:8080"
EUREKA_SERVER="http://localhost:8761"
AUTH_SERVICE="http://localhost:8081"

# Test functions
test_service_health() {
    local service_name=$1
    local url=$2
    
    echo -n "Testing $service_name health... "
    
    response=$(curl -s -o /dev/null -w "%{http_code}" "$url/actuator/health")
    
    if [ "$response" = "200" ]; then
        echo -e "${GREEN}✓ PASS${NC}"
        return 0
    else
        echo -e "${RED}✗ FAIL (HTTP $response)${NC}"
        return 1
    fi
}

test_eureka_registration() {
    echo -n "Testing Eureka service registration... "
    
    response=$(curl -s "$EUREKA_SERVER/eureka/apps" | grep -c "auth-service\|api-gateway")
    
    if [ "$response" -gt 0 ]; then
        echo -e "${GREEN}✓ PASS${NC}"
        return 0
    else
        echo -e "${RED}✗ FAIL${NC}"
        return 1
    fi
}

test_auth_registration() {
    echo -n "Testing user registration... "
    
    # Generate random email to avoid conflicts
    random_id=$(date +%s)
    test_email="test$<EMAIL>"
    
    response=$(curl -s -X POST "$API_GATEWAY/api/v1/auth/register" \
        -H "Content-Type: application/json" \
        -d "{
            \"email\": \"$test_email\",
            \"password\": \"Test123!@#\",
            \"firstName\": \"أحمد\",
            \"lastName\": \"محمد\",
            \"phoneNumber\": \"+966501234567\"
        }" \
        -w "%{http_code}" -o /tmp/register_response.json)
    
    http_code=$(echo "$response" | tail -c 4)
    
    if [ "$http_code" = "201" ]; then
        echo -e "${GREEN}✓ PASS${NC}"
        # Store the access token for login test
        access_token=$(jq -r '.accessToken' /tmp/register_response.json 2>/dev/null)
        echo "$test_email" > /tmp/test_email.txt
        echo "$access_token" > /tmp/access_token.txt
        return 0
    else
        echo -e "${RED}✗ FAIL (HTTP $http_code)${NC}"
        cat /tmp/register_response.json 2>/dev/null
        return 1
    fi
}

test_auth_login() {
    echo -n "Testing user login... "
    
    if [ ! -f /tmp/test_email.txt ]; then
        echo -e "${YELLOW}⚠ SKIP (No registered user)${NC}"
        return 1
    fi
    
    test_email=$(cat /tmp/test_email.txt)
    
    response=$(curl -s -X POST "$API_GATEWAY/api/v1/auth/login" \
        -H "Content-Type: application/json" \
        -d "{
            \"identifier\": \"$test_email\",
            \"password\": \"Test123!@#\"
        }" \
        -w "%{http_code}" -o /tmp/login_response.json)
    
    http_code=$(echo "$response" | tail -c 4)
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✓ PASS${NC}"
        return 0
    else
        echo -e "${RED}✗ FAIL (HTTP $http_code)${NC}"
        cat /tmp/login_response.json 2>/dev/null
        return 1
    fi
}

test_api_gateway_routing() {
    echo -n "Testing API Gateway routing... "
    
    # Test routing to auth service through gateway
    response=$(curl -s -o /dev/null -w "%{http_code}" "$API_GATEWAY/api/v1/auth/validate" \
        -X POST \
        -H "Content-Type: application/json" \
        -d '{"token": "dummy-token"}')
    
    # We expect 401 or 400, not 404 (which would indicate routing failure)
    if [ "$response" != "404" ]; then
        echo -e "${GREEN}✓ PASS${NC}"
        return 0
    else
        echo -e "${RED}✗ FAIL (HTTP $response)${NC}"
        return 1
    fi
}

# Main test execution
echo ""
echo "🔍 Running System Tests..."
echo ""

# Test infrastructure services
echo "📋 Infrastructure Services:"
test_service_health "Eureka Server" "$EUREKA_SERVER"
test_service_health "API Gateway" "$API_GATEWAY"

echo ""
echo "📋 Core Services:"
test_service_health "Auth Service" "$AUTH_SERVICE"

echo ""
echo "📋 Service Discovery:"
test_eureka_registration

echo ""
echo "📋 API Gateway:"
test_api_gateway_routing

echo ""
echo "📋 Authentication Flow:"
test_auth_registration
test_auth_login

echo ""
echo "🧹 Cleanup..."
rm -f /tmp/register_response.json /tmp/login_response.json /tmp/test_email.txt /tmp/access_token.txt

echo ""
echo "✅ System tests completed!"
echo ""
echo "📊 Service Status Summary:"
echo "- Eureka Server: http://localhost:8761"
echo "- API Gateway: http://localhost:8080"
echo "- Auth Service: http://localhost:8081/auth-service"
echo ""
echo "🔗 Quick Links:"
echo "- Eureka Dashboard: http://localhost:8761"
echo "- API Gateway Health: http://localhost:8080/actuator/health"
echo "- Auth Service Swagger: http://localhost:8081/auth-service/swagger-ui.html"
