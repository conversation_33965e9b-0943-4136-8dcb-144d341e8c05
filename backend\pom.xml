<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.tecnodrive</groupId>
    <artifactId>tecnodrive-backend</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>TECNO DRIVE Backend</name>
    <description>Smart Fleet Management System - Backend Services</description>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <!-- Spring Boot -->
        <spring-boot.version>3.2.0</spring-boot.version>
        <spring-cloud.version>2023.0.0</spring-cloud.version>

        <!-- API Documentation -->
        <swagger.version>2.2.0</swagger.version>

        <!-- MapStruct -->
        <mapstruct.version>1.5.5.Final</mapstruct.version>

        <!-- Lombok -->
        <lombok.version>1.18.30</lombok.version>
    </properties>

    <modules>
        <!-- Infrastructure Services -->
        <module>microservices/infrastructure/eureka-server</module>
        <module>microservices/infrastructure/api-gateway</module>
        <module>microservices/infrastructure/config-server</module>
        <module>microservices/infrastructure/monitoring-service</module>

        <!-- Core Services -->
        <module>microservices/core/auth-service</module>
        <module>microservices/core/user-service</module>
        <module>microservices/core/payment-service</module>

        <!-- Business Services -->
        <module>microservices/business/ride-service</module>
        <module>microservices/business/fleet-service</module>
        <module>microservices/business/location-service</module>
        <module>microservices/business/analytics-service</module>
        <module>microservices/business/notification-service</module>
        <module>microservices/business/parcel-service</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Boot BOM -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Spring Cloud BOM -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- MapStruct -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <!-- SpringDoc OpenAPI -->
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                <version>${swagger.version}</version>
            </dependency>

            <!-- Lombok -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
            </plugin>
        </plugins>
    </build>
</project>
