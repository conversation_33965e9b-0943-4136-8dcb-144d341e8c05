import React, { forwardRef } from 'react';
import styled, { css } from 'styled-components';

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
  variant?: 'outlined' | 'filled';
  size?: 'small' | 'medium' | 'large';
  fullWidth?: boolean;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
}

const InputContainer = styled.div<{ fullWidth?: boolean }>`
  display: flex;
  flex-direction: column;
  ${({ fullWidth }) => fullWidth && 'width: 100%;'}
`;

const Label = styled.label`
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 4px;
`;

const InputWrapper = styled.div<{ hasError?: boolean; variant?: string }>`
  position: relative;
  display: flex;
  align-items: center;

  ${({ variant, hasError }) => {
    const baseStyles = css`
      border-radius: 8px;
      transition: all 0.2s ease-in-out;
    `;

    if (variant === 'filled') {
      return css`
        ${baseStyles}
        background-color: #f9fafb;
        border: 1px solid transparent;

        &:focus-within {
          background-color: white;
          border-color: #3b82f6;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        ${hasError &&
        css`
          border-color: #ef4444;
          background-color: #fef2f2;

          &:focus-within {
            border-color: #ef4444;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
          }
        `}
      `;
    }

    return css`
      ${baseStyles}
      background-color: white;
      border: 1px solid #d1d5db;

      &:focus-within {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }

      ${hasError &&
      css`
        border-color: #ef4444;

        &:focus-within {
          border-color: #ef4444;
          box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }
      `}
    `;
  }}
`;

const StyledInput = styled.input<{ size?: string; hasStartIcon?: boolean; hasEndIcon?: boolean }>`
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  color: #374151;
  font-size: 16px;

  ${({ size }) => {
    switch (size) {
      case 'small':
        return css`
          padding: 8px 12px;
          font-size: 14px;
        `;
      case 'large':
        return css`
          padding: 16px 16px;
          font-size: 18px;
        `;
      default:
        return css`
          padding: 12px 16px;
          font-size: 16px;
        `;
    }
  }}

  ${({ hasStartIcon }) =>
    hasStartIcon &&
    css`
      padding-left: 8px;
    `}

  ${({ hasEndIcon }) =>
    hasEndIcon &&
    css`
      padding-right: 8px;
    `}

  &::placeholder {
    color: #9ca3af;
  }

  &:disabled {
    color: #9ca3af;
    cursor: not-allowed;
  }
`;

const IconWrapper = styled.div<{ position: 'start' | 'end' }>`
  display: flex;
  align-items: center;
  color: #6b7280;
  ${({ position }) =>
    position === 'start'
      ? css`
          padding-left: 12px;
        `
      : css`
          padding-right: 12px;
        `}
`;

const HelperText = styled.div<{ error?: boolean }>`
  font-size: 12px;
  margin-top: 4px;
  color: ${({ error }) => (error ? '#ef4444' : '#6b7280')};
`;

export const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      label,
      error,
      helperText,
      variant = 'outlined',
      size = 'medium',
      fullWidth = false,
      startIcon,
      endIcon,
      className,
      ...props
    },
    ref
  ) => {
    const hasError = Boolean(error);

    return (
      <InputContainer fullWidth={fullWidth} className={className}>
        {label && <Label>{label}</Label>}
        <InputWrapper hasError={hasError} variant={variant}>
          {startIcon && <IconWrapper position="start">{startIcon}</IconWrapper>}
          <StyledInput
            ref={ref}
            size={size}
            hasStartIcon={Boolean(startIcon)}
            hasEndIcon={Boolean(endIcon)}
            {...props}
          />
          {endIcon && <IconWrapper position="end">{endIcon}</IconWrapper>}
        </InputWrapper>
        {(error || helperText) && (
          <HelperText error={hasError}>{error || helperText}</HelperText>
        )}
      </InputContainer>
    );
  }
);

Input.displayName = 'Input';

export default Input;
