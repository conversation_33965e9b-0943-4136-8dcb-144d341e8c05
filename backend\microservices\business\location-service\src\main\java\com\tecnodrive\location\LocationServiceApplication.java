package com.tecnodrive.location;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;

/**
 * TECNO DRIVE - Location Service
 * 
 * This service handles:
 * - Location management and geocoding
 * - Optimal route calculation
 * - Real-time tracking
 * - Geographic data processing with PostGIS
 * 
 * <AUTHOR> DRIVE Team
 * @version 1.0.0
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableJpaAuditing
public class LocationServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(LocationServiceApplication.class, args);
    }
}
