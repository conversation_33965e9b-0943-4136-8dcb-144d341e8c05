<?xml version="1.0" encoding="UTF-8"?>

<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project version="4" relativePaths="false"> 
  <component name="LvcsProjectConfiguration"> 
    <option name="ADD_LABEL_ON_PROJECT_OPEN" value="true"/>  
    <option name="ADD_LABEL_ON_PROJECT_COMPILATION" value="true"/>  
    <option name="ADD_LABEL_ON_FILE_PACKAGE_COMPILATION" value="true"/>  
    <option name="ADD_LABEL_ON_PROJECT_MAKE" value="true"/>  
    <option name="ADD_LABEL_ON_RUNNING" value="true"/>  
    <option name="ADD_LABEL_ON_DEBUGGING" value="true"/>  
    <option name="ADD_LABEL_ON_UNIT_TEST_PASSED" value="true"/>  
    <option name="ADD_LABEL_ON_UNIT_TEST_FAILED" value="true"/> 
  </component>  
  <component name="PropertiesComponent"> 
    <property name="MemberChooser.copyJavadoc" value="false"/>  
    <property name="GoToClass.includeLibraries" value="false"/>  
    <property name="MemberChooser.showClasses" value="true"/>  
    <property name="MemberChooser.sorted" value="false"/>  
    <property name="GoToFile.includeJavaFiles" value="false"/>  
    <property name="GoToClass.toSaveIncludeLibraries" value="false"/> 
  </component>  
  <component name="ToolWindowManager"> 
    <frame x="-4" y="-4" width="1032" height="746" extended-state="6"/>  
    <editor active="false"/>  
    <layout> 
      <window_info id="CVS" active="false" anchor="bottom" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.33" order="-1"/>  
      <window_info id="TODO" active="false" anchor="bottom" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.33" order="7"/>  
      <window_info id="Project" active="false" anchor="left" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.25" order="0"/>  
      <window_info id="Find" active="false" anchor="bottom" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.33" order="1"/>  
      <window_info id="Structure" active="false" anchor="left" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.25" order="1"/>  
      <window_info id="Messages" active="false" anchor="bottom" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.33" order="-1"/>  
      <window_info id="Inspection" active="false" anchor="bottom" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.4" order="6"/>  
      <window_info id="Aspects" active="false" anchor="right" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.33" order="-1"/>  
      <window_info id="Ant Build" active="false" anchor="right" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.25" order="1"/>  
      <window_info id="Run" active="false" anchor="bottom" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.33" order="2"/>  
      <window_info id="Hierarchy" active="false" anchor="right" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.25" order="2"/>  
      <window_info id="Debug" active="false" anchor="bottom" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.4" order="4"/>  
      <window_info id="Commander" active="false" anchor="right" auto_hide="false" internal_type="sliding" type="sliding" visible="false" weight="0.4" order="0"/>  
      <window_info id="Web" active="false" anchor="left" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.25" order="2"/>  
      <window_info id="Message" active="false" anchor="bottom" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.33" order="0"/>  
      <window_info id="EJB" active="false" anchor="bottom" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.25" order="3"/>  
      <window_info id="Cvs" active="false" anchor="bottom" auto_hide="false" internal_type="docked" type="docked" visible="false" weight="0.25" order="5"/> 
    </layout> 
  </component>  
  <component name="ErrorTreeViewConfiguration"> 
    <option name="IS_AUTOSCROLL_TO_SOURCE" value="false"/>  
    <option name="HIDE_WARNINGS" value="false"/> 
  </component>  
  <component name="StructureViewFactory"> 
    <option name="SORT_MODE" value="0"/>  
    <option name="GROUP_INHERITED" value="true"/>  
    <option name="AUTOSCROLL_MODE" value="true"/>  
    <option name="SHOW_FIELDS" value="true"/>  
    <option name="AUTOSCROLL_FROM_SOURCE" value="false"/>  
    <option name="GROUP_GETTERS_AND_SETTERS" value="true"/>  
    <option name="SHOW_INHERITED" value="false"/>  
    <option name="HIDE_NOT_PUBLIC" value="false"/> 
  </component>  
  <component name="ProjectViewSettings"> 
    <navigator currentView="ProjectPane" flattenPackages="false" showMembers="false" showStructure="false" autoscrollToSource="false" splitterProportion="0.5"/>  
    <view id="ProjectPane"> 
      <expanded_node type="directory" url="file://$PROJECT_DIR$"/> 
    </view>  
    <view id="SourcepathPane"/>  
    <view id="ClasspathPane"/> 
  </component>  
  <component name="Commander"> 
    <leftPanel view="Project"/>  
    <rightPanel view="Project"/>  
    <splitter proportion="0.5"/> 
  </component>  
  <component name="AspectsView"/>  
  <component name="SelectInManager"/>  
  <component name="HierarchyBrowserManager"> 
    <option name="SHOW_PACKAGES" value="false"/>  
    <option name="IS_AUTOSCROLL_TO_SOURCE" value="false"/>  
    <option name="SORT_ALPHABETICALLY" value="false"/> 
  </component>  
  <component name="TodoView" selected-index="0"> 
    <todo-panel id="selected-file"> 
      <are-packages-shown value="false"/>  
      <flatten-packages value="false"/>  
      <is-autoscroll-to-source value="true"/> 
    </todo-panel>  
    <todo-panel id="all"> 
      <are-packages-shown value="true"/>  
      <flatten-packages value="false"/>  
      <is-autoscroll-to-source value="true"/> 
    </todo-panel> 
  </component>  
  <component name="editorManager"/>  
  <component name="editorHistoryManager"/>  
  <component name="DaemonCodeAnalyzer"> 
    <disable_hints/> 
  </component>  
  <component name="InspectionManager"> 
    <option name="AUTOSCROLL_TO_SOURCE" value="false"/>  
    <option name="SPLITTER_PROPORTION" value="0.5"/>  
    <profile name="Default"/> 
  </component>  
  <component name="BookmarkManager"/>  
  <component name="DebuggerManager"> 
    <line_breakpoints/>  
    <exception_breakpoints> 
      <breakpoint_any> 
        <option name="NOTIFY_CAUGHT" value="true"/>  
        <option name="NOTIFY_UNCAUGHT" value="true"/>  
        <option name="ENABLED" value="false"/>  
        <option name="SUSPEND_VM" value="true"/>  
        <option name="COUNT_FILTER_ENABLED" value="false"/>  
        <option name="COUNT_FILTER" value="0"/>  
        <option name="CONDITION_ENABLED" value="false"/>  
        <option name="CONDITION"/>  
        <option name="LOG_ENABLED" value="false"/>  
        <option name="LOG_EXPRESSION_ENABLED" value="false"/>  
        <option name="LOG_MESSAGE"/>  
        <option name="CLASS_FILTERS_ENABLED" value="false"/>  
        <option name="INVERSE_CLASS_FILLTERS" value="false"/>  
        <option name="SUSPEND_POLICY" value="SuspendAll"/> 
      </breakpoint_any> 
    </exception_breakpoints>  
    <field_breakpoints/>  
    <method_breakpoints/> 
  </component>  
  <component name="DebuggerSettings"> 
    <option name="TRACING_FILTERS_ENABLED" value="true"/>  
    <option name="TOSTRING_CLASSES_ENABLED" value="false"/>  
    <option name="VALUE_LOOKUP_DELAY" value="700"/>  
    <option name="DEBUGGER_TRANSPORT" value="0"/>  
    <option name="FORCE_CLASSIC_VM" value="true"/>  
    <option name="HIDE_DEBUGGER_ON_PROCESS_TERMINATION" value="false"/>  
    <option name="SKIP_SYNTHETIC_METHODS" value="true"/>  
    <option name="SKIP_CONSTRUCTORS" value="false"/>  
    <option name="STEP_THREAD_SUSPEND_POLICY" value="SuspendThread"/>  
    <default_breakpoint_settings> 
      <option name="NOTIFY_CAUGHT" value="true"/>  
      <option name="NOTIFY_UNCAUGHT" value="true"/>  
      <option name="WATCH_MODIFICATION" value="true"/>  
      <option name="WATCH_ACCESS" value="true"/>  
      <option name="WATCH_ENTRY" value="true"/>  
      <option name="WATCH_EXIT" value="true"/>  
      <option name="ENABLED" value="true"/>  
      <option name="SUSPEND_VM" value="true"/>  
      <option name="COUNT_FILTER_ENABLED" value="false"/>  
      <option name="COUNT_FILTER" value="0"/>  
      <option name="CONDITION_ENABLED" value="false"/>  
      <option name="CONDITION"/>  
      <option name="LOG_ENABLED" value="false"/>  
      <option name="LOG_EXPRESSION_ENABLED" value="false"/>  
      <option name="LOG_MESSAGE"/>  
      <option name="CLASS_FILTERS_ENABLED" value="false"/>  
      <option name="INVERSE_CLASS_FILLTERS" value="false"/>  
      <option name="SUSPEND_POLICY" value="SuspendAll"/> 
    </default_breakpoint_settings>  
    <filter> 
      <option name="PATTERN" value="com.sun.*"/>  
      <option name="ENABLED" value="true"/> 
    </filter>  
    <filter> 
      <option name="PATTERN" value="java.*"/>  
      <option name="ENABLED" value="true"/> 
    </filter>  
    <filter> 
      <option name="PATTERN" value="javax.*"/>  
      <option name="ENABLED" value="true"/> 
    </filter>  
    <filter> 
      <option name="PATTERN" value="org.omg.*"/>  
      <option name="ENABLED" value="true"/> 
    </filter>  
    <filter> 
      <option name="PATTERN" value="sun.*"/>  
      <option name="ENABLED" value="true"/> 
    </filter>  
    <filter> 
      <option name="PATTERN" value="junit.*"/>  
      <option name="ENABLED" value="true"/> 
    </filter> 
  </component>  
  <component name="CompilerWorkspaceConfiguration"> 
    <option name="COMPILE_IN_BACKGROUND" value="false"/>  
    <option name="AUTO_SHOW_ERRORS_IN_EDITOR" value="true"/> 
  </component>  
  <component name="RunManager"> 
    <activeType name="Application"/>  
    <configuration selected="false" default="true" type="Applet" factoryName="Applet"> 
      <module name=""/>  
      <option name="MAIN_CLASS_NAME"/>  
      <option name="HTML_FILE_NAME"/>  
      <option name="HTML_USED" value="false"/>  
      <option name="WIDTH" value="400"/>  
      <option name="HEIGHT" value="300"/>  
      <option name="POLICY_FILE" value="$APPLICATION_HOME_DIR$/bin/appletviewer.policy"/>  
      <option name="VM_PARAMETERS"/> 
    </configuration>  
    <configuration selected="false" default="true" type="Remote" factoryName="Remote"> 
      <option name="USE_SOCKET_TRANSPORT" value="true"/>  
      <option name="SERVER_MODE" value="false"/>  
      <option name="SHMEM_ADDRESS" value="javadebug"/>  
      <option name="HOST" value="localhost"/>  
      <option name="PORT" value="5005"/> 
    </configuration>  
    <configuration selected="false" default="true" type="Application" factoryName="Application"> 
      <option name="MAIN_CLASS_NAME"/>  
      <option name="VM_PARAMETERS"/>  
      <option name="PROGRAM_PARAMETERS"/>  
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$"/>  
      <module name=""/> 
    </configuration>  
    <configuration selected="false" default="true" type="JUnit" factoryName="JUnit"> 
      <module name=""/>  
      <option name="PACKAGE_NAME"/>  
      <option name="MAIN_CLASS_NAME"/>  
      <option name="METHOD_NAME"/>  
      <option name="TEST_OBJECT" value="class"/>  
      <option name="VM_PARAMETERS"/>  
      <option name="PARAMETERS"/>  
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$"/>  
      <option name="ADDITIONAL_CLASS_PATH"/>  
      <option name="TEST_SEARCH_SCOPE"> 
        <value defaultName="wholeProject"/> 
      </option> 
    </configuration> 
  </component>  
  <component name="VcsManagerConfiguration"> 
    <option name="ACTIVE_VCS_NAME" value=""/>  
    <option name="STATE" value="0"/> 
  </component>  
  <component name="VssConfiguration"> 
    <CheckoutOptions> 
      <option name="COMMENT" value=""/>  
      <option name="DO_NOT_GET_LATEST_VERSION" value="false"/>  
      <option name="REPLACE_WRITABLE" value="false"/>  
      <option name="RECURSIVE" value="false"/> 
    </CheckoutOptions>  
    <CheckinOptions> 
      <option name="COMMENT" value=""/>  
      <option name="KEEP_CHECKED_OUT" value="false"/>  
      <option name="RECURSIVE" value="false"/> 
    </CheckinOptions>  
    <AddOptions> 
      <option name="COMMENT" value=""/>  
      <option name="STORE_ONLY_LATEST_VERSION" value="false"/>  
      <option name="CHECK_OUT_IMMEDIATELY" value="false"/>  
      <option name="FILE_TYPE" value="0"/> 
    </AddOptions>  
    <UndocheckoutOptions> 
      <option name="MAKE_WRITABLE" value="false"/>  
      <option name="REPLACE_LOCAL_COPY" value="0"/>  
      <option name="RECURSIVE" value="false"/> 
    </UndocheckoutOptions>  
    <DiffOptions> 
      <option name="IGNORE_WHITE_SPACE" value="false"/>  
      <option name="IGNORE_CASE" value="false"/> 
    </DiffOptions>  
    <GetOptions> 
      <option name="REPLACE_WRITABLE" value="0"/>  
      <option name="MAKE_WRITABLE" value="false"/>  
      <option name="RECURSIVE" value="false"/> 
    </GetOptions>  
    <option name="CLIENT_PATH" value=""/>  
    <option name="SRCSAFEINI_PATH" value=""/>  
    <option name="USER_NAME" value=""/>  
    <option name="PWD" value=""/>  
    <option name="SHOW_CHECKOUT_OPTIONS" value="true"/>  
    <option name="SHOW_ADD_OPTIONS" value="true"/>  
    <option name="SHOW_UNDOCHECKOUT_OPTIONS" value="true"/>  
    <option name="SHOW_DIFF_OPTIONS" value="true"/>  
    <option name="SHOW_GET_OPTIONS" value="true"/>  
    <option name="USE_EXTERNAL_DIFF" value="false"/>  
    <option name="EXTERNAL_DIFF_PATH" value=""/>  
    <option name="REUSE_LAST_COMMENT" value="false"/>  
    <option name="PUT_FOCUS_INTO_COMMENT" value="false"/>  
    <option name="SHOW_CHECKIN_OPTIONS" value="true"/>  
    <option name="LAST_COMMIT_MESSAGE" value=""/>  
    <option name="CHECKIN_DIALOG_SPLITTER_PROPORTION" value="0.8"/> 
  </component>  
  <component name="CheckinPanelState"/>  
  <component name="WebViewSettings"> 
    <webview flattenPackages="false" showMembers="false" autoscrollToSource="false"/> 
  </component>  
  <component name="EjbViewSettings"> 
    <EjbView showMembers="false" autoscrollToSource="false"/> 
  </component>  
  <component name="AppServerRunManager"/>  
  <component name="StarteamConfiguration"> 
    <option name="SERVER" value=""/>  
    <option name="PORT" value="49201"/>  
    <option name="USER" value=""/>  
    <option name="PASSWORD" value=""/>  
    <option name="PROJECT" value=""/>  
    <option name="VIEW" value=""/>  
    <option name="ALTERNATIVE_WORKING_PATH" value=""/>  
    <option name="PUT_FOCUS_INTO_COMMENT" value="false"/>  
    <option name="SHOW_CHECKIN_OPTIONS" value="true"/>  
    <option name="LAST_COMMIT_MESSAGE" value=""/>  
    <option name="CHECKIN_DIALOG_SPLITTER_PROPORTION" value="0.8"/> 
  </component>  
  <component name="Cvs2Configuration"> 
    <option name="ON_FILE_ADDING" value="0"/>  
    <option name="ON_FILE_REMOVING" value="0"/>  
    <option name="PRUNE_EMPTY_DIRECTORIES" value="true"/>  
    <option name="SHOW_UPDATE_OPTIONS" value="true"/>  
    <option name="SHOW_ADD_OPTIONS" value="true"/>  
    <option name="SHOW_REMOVE_OPTIONS" value="true"/>  
    <option name="MERGING_MODE" value="0"/>  
    <option name="MERGE_WITH_BRANCH1_NAME" value="HEAD"/>  
    <option name="MERGE_WITH_BRANCH2_NAME" value="HEAD"/>  
    <option name="RESET_STICKY" value="false"/>  
    <option name="CREATE_NEW_DIRECTORIES" value="true"/>  
    <option name="DEFAULT_TEXT_FILE_SUBSTITUTION" value="kv"/>  
    <option name="PROCESS_UNKNOWN_FILES" value="false"/>  
    <option name="PROCESS_DELETED_FILES" value="false"/>  
    <option name="SHOW_EDIT_DIALOG" value="true"/>  
    <option name="RESERVED_EDIT" value="false"/>  
    <option name="FILE_HISTORY_SPLITTER_PROPORTION" value="0.6"/>  
    <option name="SHOW_CHECKOUT_OPTIONS" value="true"/>  
    <option name="CHECKOUT_DATE_OR_REVISION_SETTINGS"> 
      <value> 
        <option name="BRANCH" value=""/>  
        <option name="DATE" value=""/>  
        <option name="USE_BRANCH" value="false"/>  
        <option name="USE_DATE" value="false"/> 
      </value> 
    </option>  
    <option name="UPDATE_DATE_OR_REVISION_SETTINGS"> 
      <value> 
        <option name="BRANCH" value=""/>  
        <option name="DATE" value=""/>  
        <option name="USE_BRANCH" value="false"/>  
        <option name="USE_DATE" value="false"/> 
      </value> 
    </option>  
    <option name="SHOW_CHANGES_REVISION_SETTINGS"> 
      <value> 
        <option name="BRANCH" value=""/>  
        <option name="DATE" value=""/>  
        <option name="USE_BRANCH" value="false"/>  
        <option name="USE_DATE" value="false"/> 
      </value> 
    </option>  
    <option name="SHOW_OUTPUT" value="false"/>  
    <option name="SHOW_FILE_HISTORY_AS_TREE" value="false"/>  
    <option name="UPDATE_GROUP_BY_PACKAGES" value="false"/>  
    <option name="ADD_WATCH_INDEX" value="0"/>  
    <option name="REMOVE_WATCH_INDEX" value="0"/>  
    <option name="UPDATE_KEYWORD_SUBSTITUTION"/>  
    <option name="MAKE_NEW_FILES_READONLY" value="false"/>  
    <option name="SHOW_CORRUPTED_PROJECT_FILES" value="0"/>  
    <option name="TAG_AFTER_FILE_COMMIT" value="false"/>  
    <option name="TAG_AFTER_FILE_COMMIT_NAME" value=""/>  
    <option name="TAG_AFTER_PROJECT_COMMIT" value="false"/>  
    <option name="TAG_AFTER_PROJECT_COMMIT_NAME" value=""/>  
    <option name="PUT_FOCUS_INTO_COMMENT" value="false"/>  
    <option name="SHOW_CHECKIN_OPTIONS" value="true"/>  
    <option name="FORCE_NON_EMPTY_COMMENT" value="false"/>  
    <option name="LAST_COMMIT_MESSAGE" value=""/>  
    <option name="SAVE_LAST_COMMIT_MESSAGE" value="true"/>  
    <option name="CHECKIN_DIALOG_SPLITTER_PROPORTION" value="0.8"/>  
    <option name="OPTIMIZE_IMPORTS_BEFORE_PROJECT_COMMIT" value="false"/>  
    <option name="OPTIMIZE_IMPORTS_BEFORE_FILE_COMMIT" value="false"/>  
    <option name="REFORMAT_BEFORE_PROJECT_COMMIT" value="false"/>  
    <option name="REFORMAT_BEFORE_FILE_COMMIT" value="false"/>  
    <option name="FILE_HISTORY_DIALOG_COMMENTS_SPLITTER_PROPORTION" value="0.8"/>  
    <option name="FILE_HISTORY_DIALOG_SPLITTER_PROPORTION" value="0.5"/> 
  </component>  
  <component name="CvsTabbedWindow"/>  
  <component name="SvnConfiguration"> 
    <option name="USER" value=""/>  
    <option name="PASSWORD" value=""/>  
    <option name="AUTO_ADD_FILES" value="0"/>  
    <option name="AUTO_DEL_FILES" value="0"/> 
  </component>  
  <component name="PerforceConfiguration"> 
    <option name="PORT" value="magic:1666"/>  
    <option name="USER" value=""/>  
    <option name="PASSWORD" value=""/>  
    <option name="CLIENT" value=""/>  
    <option name="TRACE" value="false"/>  
    <option name="PERFORCE_STATUS" value="true"/>  
    <option name="CHANGELIST_OPTION" value="false"/>  
    <option name="SYSTEMROOT" value=""/>  
    <option name="P4_EXECUTABLE" value="p4"/>  
    <option name="SHOW_BRANCH_HISTORY" value="false"/>  
    <option name="GENERATE_COMMENT" value="false"/>  
    <option name="SYNC_OPTION" value="Sync"/>  
    <option name="PUT_FOCUS_INTO_COMMENT" value="false"/>  
    <option name="SHOW_CHECKIN_OPTIONS" value="true"/>  
    <option name="FORCE_NON_EMPTY_COMMENT" value="true"/>  
    <option name="LAST_COMMIT_MESSAGE" value=""/>  
    <option name="SAVE_LAST_COMMIT_MESSAGE" value="true"/>  
    <option name="CHECKIN_DIALOG_SPLITTER_PROPORTION" value="0.8"/>  
    <option name="OPTIMIZE_IMPORTS_BEFORE_PROJECT_COMMIT" value="false"/>  
    <option name="OPTIMIZE_IMPORTS_BEFORE_FILE_COMMIT" value="false"/>  
    <option name="REFORMAT_BEFORE_PROJECT_COMMIT" value="false"/>  
    <option name="REFORMAT_BEFORE_FILE_COMMIT" value="false"/>  
    <option name="FILE_HISTORY_DIALOG_COMMENTS_SPLITTER_PROPORTION" value="0.8"/>  
    <option name="FILE_HISTORY_DIALOG_SPLITTER_PROPORTION" value="0.5"/> 
  </component> 
</project>
