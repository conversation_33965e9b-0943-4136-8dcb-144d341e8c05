{"version": "2.0.0", "tasks": [{"label": "Build All Microservices", "type": "shell", "command": "mvn", "args": ["-f", "backend/pom.xml", "clean", "install", "-DskipTests"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$maven"}, {"label": "Start Backend Services", "type": "shell", "command": "docker-compose", "args": ["up", "--build"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "isBackground": true, "problemMatcher": []}, {"label": "Start Admin Dashboard", "type": "shell", "command": "yarn", "args": ["--cwd", "frontend/admin-dashboard", "start"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "isBackground": true, "problemMatcher": []}, {"label": "Start Driver App", "type": "shell", "command": "yarn", "args": ["--cwd", "frontend/driver-app", "start"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "isBackground": true, "problemMatcher": []}, {"label": "Start Passenger App", "type": "shell", "command": "yarn", "args": ["--cwd", "frontend/passenger-app", "start"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "isBackground": true, "problemMatcher": []}, {"label": "Stop All Services", "type": "shell", "command": "docker-compose", "args": ["down"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Run Tests - Backend", "type": "shell", "command": "mvn", "args": ["-f", "backend/pom.xml", "test"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$maven"}, {"label": "Run Tests - Frontend", "type": "shell", "command": "yarn", "args": ["--cwd", "frontend/admin-dashboard", "test"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}]}