import React, { useEffect, useRef, useState } from 'react';
import styled from 'styled-components';

// Map provider interfaces
interface MapProvider {
  initialize(container: HTMLElement, options: MapOptions): void;
  addMarker(coordinates: Coordinates, options?: MarkerOptions): string;
  removeMarker(markerId: string): void;
  addRoute(coordinates: Coordinates[], options?: RouteOptions): string;
  removeRoute(routeId: string): void;
  setCenter(coordinates: Coordinates, zoom?: number): void;
  fitBounds(bounds: Bounds): void;
  on(event: string, callback: Function): void;
  off(event: string, callback: Function): void;
}

interface MapOptions {
  center: Coordinates;
  zoom: number;
  style?: string;
  controls?: boolean;
  interactive?: boolean;
}

interface Coordinates {
  lat: number;
  lng: number;
}

interface MarkerOptions {
  icon?: string;
  title?: string;
  popup?: string;
  draggable?: boolean;
  color?: string;
}

interface RouteOptions {
  color?: string;
  weight?: number;
  opacity?: number;
  dashArray?: string;
}

interface Bounds {
  north: number;
  south: number;
  east: number;
  west: number;
}

interface InteractiveMapProps {
  provider?: 'google' | 'mapbox' | 'openstreetmap';
  center: Coordinates;
  zoom?: number;
  height?: string;
  width?: string;
  markers?: MapMarker[];
  routes?: MapRoute[];
  onMarkerClick?: (marker: MapMarker) => void;
  onMapClick?: (coordinates: Coordinates) => void;
  onBoundsChange?: (bounds: Bounds) => void;
  style?: React.CSSProperties;
  className?: string;
}

interface MapMarker {
  id: string;
  coordinates: Coordinates;
  title?: string;
  popup?: string;
  icon?: string;
  color?: string;
  draggable?: boolean;
}

interface MapRoute {
  id: string;
  coordinates: Coordinates[];
  color?: string;
  weight?: number;
  opacity?: number;
  dashArray?: string;
}

const MapContainer = styled.div<{ height?: string; width?: string }>`
  width: ${({ width }) => width || '100%'};
  height: ${({ height }) => height || '400px'};
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  background-color: #f5f5f5;

  .map-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #666;
    font-size: 16px;
  }

  .map-error {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #dc3545;
    font-size: 16px;
    text-align: center;
    padding: 20px;
  }
`;

const MapControls = styled.div`
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const ControlButton = styled.button`
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;

  &:hover {
    background: #f8f9fa;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: translateY(1px);
  }
`;

const LayerSelector = styled.select`
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-size: 14px;
`;

// Google Maps Provider Implementation
class GoogleMapsProvider implements MapProvider {
  private map: google.maps.Map | null = null;
  private markers: Map<string, google.maps.Marker> = new Map();
  private routes: Map<string, google.maps.Polyline> = new Map();

  initialize(container: HTMLElement, options: MapOptions): void {
    this.map = new google.maps.Map(container, {
      center: options.center,
      zoom: options.zoom,
      mapTypeControl: options.controls,
      streetViewControl: options.controls,
      fullscreenControl: options.controls,
    });
  }

  addMarker(coordinates: Coordinates, options?: MarkerOptions): string {
    if (!this.map) return '';

    const markerId = `marker_${Date.now()}_${Math.random()}`;
    const marker = new google.maps.Marker({
      position: coordinates,
      map: this.map,
      title: options?.title,
      draggable: options?.draggable,
    });

    if (options?.popup) {
      const infoWindow = new google.maps.InfoWindow({
        content: options.popup,
      });

      marker.addListener('click', () => {
        infoWindow.open(this.map, marker);
      });
    }

    this.markers.set(markerId, marker);
    return markerId;
  }

  removeMarker(markerId: string): void {
    const marker = this.markers.get(markerId);
    if (marker) {
      marker.setMap(null);
      this.markers.delete(markerId);
    }
  }

  addRoute(coordinates: Coordinates[], options?: RouteOptions): string {
    if (!this.map) return '';

    const routeId = `route_${Date.now()}_${Math.random()}`;
    const polyline = new google.maps.Polyline({
      path: coordinates,
      geodesic: true,
      strokeColor: options?.color || '#FF0000',
      strokeOpacity: options?.opacity || 1.0,
      strokeWeight: options?.weight || 2,
    });

    polyline.setMap(this.map);
    this.routes.set(routeId, polyline);
    return routeId;
  }

  removeRoute(routeId: string): void {
    const route = this.routes.get(routeId);
    if (route) {
      route.setMap(null);
      this.routes.delete(routeId);
    }
  }

  setCenter(coordinates: Coordinates, zoom?: number): void {
    if (this.map) {
      this.map.setCenter(coordinates);
      if (zoom !== undefined) {
        this.map.setZoom(zoom);
      }
    }
  }

  fitBounds(bounds: Bounds): void {
    if (this.map) {
      const googleBounds = new google.maps.LatLngBounds(
        { lat: bounds.south, lng: bounds.west },
        { lat: bounds.north, lng: bounds.east }
      );
      this.map.fitBounds(googleBounds);
    }
  }

  on(event: string, callback: Function): void {
    if (this.map) {
      this.map.addListener(event, callback);
    }
  }

  off(event: string, callback: Function): void {
    if (this.map) {
      google.maps.event.clearListeners(this.map, event);
    }
  }
}

// Mapbox Provider Implementation (simplified)
class MapboxProvider implements MapProvider {
  // Implementation would be similar to Google Maps
  // but using Mapbox GL JS API
  initialize(container: HTMLElement, options: MapOptions): void {
    // Mapbox implementation
  }

  addMarker(coordinates: Coordinates, options?: MarkerOptions): string {
    return '';
  }

  removeMarker(markerId: string): void {}
  addRoute(coordinates: Coordinates[], options?: RouteOptions): string { return ''; }
  removeRoute(routeId: string): void {}
  setCenter(coordinates: Coordinates, zoom?: number): void {}
  fitBounds(bounds: Bounds): void {}
  on(event: string, callback: Function): void {}
  off(event: string, callback: Function): void {}
}

export const InteractiveMap: React.FC<InteractiveMapProps> = ({
  provider = 'google',
  center,
  zoom = 10,
  height = '400px',
  width = '100%',
  markers = [],
  routes = [],
  onMarkerClick,
  onMapClick,
  onBoundsChange,
  style,
  className,
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapProviderRef = useRef<MapProvider | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentLayer, setCurrentLayer] = useState('roadmap');

  useEffect(() => {
    if (!mapRef.current) return;

    const initializeMap = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Initialize the appropriate map provider
        switch (provider) {
          case 'google':
            mapProviderRef.current = new GoogleMapsProvider();
            break;
          case 'mapbox':
            mapProviderRef.current = new MapboxProvider();
            break;
          default:
            throw new Error(`Unsupported map provider: ${provider}`);
        }

        // Initialize the map
        mapProviderRef.current.initialize(mapRef.current, {
          center,
          zoom,
          controls: true,
          interactive: true,
        });

        // Set up event listeners
        if (onMapClick) {
          mapProviderRef.current.on('click', (event: any) => {
            onMapClick({ lat: event.latLng.lat(), lng: event.latLng.lng() });
          });
        }

        setIsLoading(false);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to initialize map');
        setIsLoading(false);
      }
    };

    initializeMap();
  }, [provider, center, zoom, onMapClick]);

  // Update markers when they change
  useEffect(() => {
    if (!mapProviderRef.current) return;

    // Clear existing markers
    // Add new markers
    markers.forEach((marker) => {
      const markerId = mapProviderRef.current!.addMarker(marker.coordinates, {
        title: marker.title,
        popup: marker.popup,
        icon: marker.icon,
        color: marker.color,
        draggable: marker.draggable,
      });

      // Store marker ID for potential removal
      (marker as any)._mapMarkerId = markerId;
    });
  }, [markers]);

  // Update routes when they change
  useEffect(() => {
    if (!mapProviderRef.current) return;

    // Clear existing routes
    // Add new routes
    routes.forEach((route) => {
      const routeId = mapProviderRef.current!.addRoute(route.coordinates, {
        color: route.color,
        weight: route.weight,
        opacity: route.opacity,
        dashArray: route.dashArray,
      });

      // Store route ID for potential removal
      (route as any)._mapRouteId = routeId;
    });
  }, [routes]);

  const handleLayerChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setCurrentLayer(event.target.value);
    // Implement layer switching logic
  };

  const handleZoomIn = () => {
    // Implement zoom in
  };

  const handleZoomOut = () => {
    // Implement zoom out
  };

  const handleResetView = () => {
    if (mapProviderRef.current) {
      mapProviderRef.current.setCenter(center, zoom);
    }
  };

  if (error) {
    return (
      <MapContainer height={height} width={width} style={style} className={className}>
        <div className="map-error">
          Error loading map: {error}
        </div>
      </MapContainer>
    );
  }

  return (
    <MapContainer height={height} width={width} style={style} className={className}>
      {isLoading && (
        <div className="map-loading">
          Loading map...
        </div>
      )}
      
      <div ref={mapRef} style={{ width: '100%', height: '100%' }} />
      
      <MapControls>
        <LayerSelector value={currentLayer} onChange={handleLayerChange}>
          <option value="roadmap">Road Map</option>
          <option value="satellite">Satellite</option>
          <option value="hybrid">Hybrid</option>
          <option value="terrain">Terrain</option>
        </LayerSelector>
        
        <ControlButton onClick={handleZoomIn} title="Zoom In">
          +
        </ControlButton>
        
        <ControlButton onClick={handleZoomOut} title="Zoom Out">
          -
        </ControlButton>
        
        <ControlButton onClick={handleResetView} title="Reset View">
          ⌂
        </ControlButton>
      </MapControls>
    </MapContainer>
  );
};

export default InteractiveMap;
