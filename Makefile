# TECNO DRIVE - Development Makefile

.PHONY: help setup dev prod clean test build deploy

# Default target
help:
	@echo "🚗 TECNO DRIVE - Available Commands:"
	@echo ""
	@echo "Setup Commands:"
	@echo "  make setup          - Initial project setup"
	@echo "  make install        - Install all dependencies"
	@echo ""
	@echo "Development Commands:"
	@echo "  make dev            - Start development environment"
	@echo "  make dev-services   - Start only infrastructure services"
	@echo "  make dev-frontend   - Start all frontend applications"
	@echo "  make dev-backend    - Start all backend services"
	@echo ""
	@echo "Build Commands:"
	@echo "  make build          - Build all services"
	@echo "  make build-backend  - Build backend services only"
	@echo "  make build-frontend - Build frontend applications only"
	@echo ""
	@echo "Test Commands:"
	@echo "  make test           - Run all tests"
	@echo "  make test-backend   - Run backend tests"
	@echo "  make test-frontend  - Run frontend tests"
	@echo "  make test-e2e       - Run end-to-end tests"
	@echo ""
	@echo "Production Commands:"
	@echo "  make prod           - Start production environment"
	@echo "  make deploy         - Deploy to production"
	@echo ""
	@echo "Utility Commands:"
	@echo "  make clean          - Clean up containers and volumes"
	@echo "  make logs           - View all service logs"
	@echo "  make status         - Check service status"

# Setup Commands
setup:
	@echo "🔧 Setting up TECNO DRIVE development environment..."
	@mkdir -p backend/microservices/{core,business,infrastructure}
	@mkdir -p frontend/{admin-dashboard,driver-app,passenger-app,operator-dashboard}
	@mkdir -p database/{migrations,seeds,backups}
	@mkdir -p infrastructure/{terraform,kubernetes,docker,monitoring}
	@mkdir -p tools/{scripts,monitoring,testing}
	@echo "✅ Project structure created!"

install:
	@echo "📦 Installing dependencies..."
	@echo "Installing backend dependencies..."
	@if [ -f backend/pom.xml ]; then mvn -f backend/pom.xml dependency:resolve; fi
	@echo "Installing frontend dependencies..."
	@if [ -d frontend/admin-dashboard ]; then cd frontend/admin-dashboard && yarn install; fi
	@if [ -d frontend/driver-app ]; then cd frontend/driver-app && yarn install; fi
	@if [ -d frontend/passenger-app ]; then cd frontend/passenger-app && yarn install; fi
	@echo "✅ Dependencies installed!"

# Development Commands
dev: dev-services
	@echo "🚀 Starting full development environment..."
	@sleep 10
	@make dev-backend &
	@make dev-frontend &
	@echo "✅ Development environment started!"
	@echo "🌐 Access points:"
	@echo "  - Admin Dashboard: http://localhost:3000"
	@echo "  - Driver App: http://localhost:3001"
	@echo "  - Passenger App: http://localhost:3002"
	@echo "  - API Gateway: http://localhost:8080"
	@echo "  - PgAdmin: http://localhost:5050"
	@echo "  - Grafana: http://localhost:3000"

dev-services:
	@echo "🐳 Starting infrastructure services..."
	@docker-compose -f docker-compose.dev.yml up -d
	@echo "✅ Infrastructure services started!"

dev-frontend:
	@echo "⚛️ Starting frontend applications..."
	@if [ -d frontend/admin-dashboard ]; then cd frontend/admin-dashboard && yarn start & fi
	@if [ -d frontend/driver-app ]; then cd frontend/driver-app && yarn start & fi
	@if [ -d frontend/passenger-app ]; then cd frontend/passenger-app && yarn start & fi

dev-backend:
	@echo "☕ Starting backend services..."
	@docker-compose up --build -d

# Build Commands
build: build-backend build-frontend

build-backend:
	@echo "🔨 Building backend services..."
	@if [ -f backend/pom.xml ]; then mvn -f backend/pom.xml clean install -DskipTests; fi
	@echo "✅ Backend services built!"

build-frontend:
	@echo "🔨 Building frontend applications..."
	@if [ -d frontend/admin-dashboard ]; then cd frontend/admin-dashboard && yarn build; fi
	@if [ -d frontend/driver-app ]; then cd frontend/driver-app && yarn build; fi
	@if [ -d frontend/passenger-app ]; then cd frontend/passenger-app && yarn build; fi
	@echo "✅ Frontend applications built!"

# Test Commands
test: test-backend test-frontend

test-backend:
	@echo "🧪 Running backend tests..."
	@if [ -f backend/pom.xml ]; then mvn -f backend/pom.xml test; fi

test-frontend:
	@echo "🧪 Running frontend tests..."
	@if [ -d frontend/admin-dashboard ]; then cd frontend/admin-dashboard && yarn test --watchAll=false; fi
	@if [ -d frontend/driver-app ]; then cd frontend/driver-app && yarn test --watchAll=false; fi
	@if [ -d frontend/passenger-app ]; then cd frontend/passenger-app && yarn test --watchAll=false; fi

test-e2e:
	@echo "🧪 Running end-to-end tests..."
	@if [ -d tools/testing ]; then cd tools/testing && yarn cypress:run; fi

# Production Commands
prod:
	@echo "🚀 Starting production environment..."
	@docker-compose -f docker-compose.yml up -d
	@echo "✅ Production environment started!"

deploy:
	@echo "🚀 Deploying to production..."
	@echo "Building production images..."
	@make build
	@echo "Deploying with Docker Compose..."
	@docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
	@echo "✅ Deployment complete!"

# Utility Commands
clean:
	@echo "🧹 Cleaning up..."
	@docker-compose down -v
	@docker-compose -f docker-compose.dev.yml down -v
	@docker system prune -f
	@echo "✅ Cleanup complete!"

logs:
	@echo "📋 Viewing service logs..."
	@docker-compose logs -f

status:
	@echo "📊 Service Status:"
	@docker-compose ps

# Database Commands
db-migrate:
	@echo "🗃️ Running database migrations..."
	@if [ -f backend/pom.xml ]; then mvn -f backend/pom.xml flyway:migrate; fi

db-seed:
	@echo "🌱 Seeding database..."
	@if [ -d database/seeds ]; then psql -h localhost -p 5433 -U dev -d tecnodrive_dev -f database/seeds/sample_data.sql; fi

db-backup:
	@echo "💾 Creating database backup..."
	@mkdir -p database/backups
	@pg_dump -h localhost -p 5433 -U dev tecnodrive_dev > database/backups/backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "✅ Database backup created!"

# Monitoring Commands
monitor:
	@echo "📊 Opening monitoring dashboards..."
	@open http://localhost:3000  # Grafana
	@open http://localhost:9090  # Prometheus
	@open http://localhost:15672 # RabbitMQ

# Development Utilities
format:
	@echo "🎨 Formatting code..."
	@if [ -f backend/pom.xml ]; then mvn -f backend/pom.xml spotless:apply; fi
	@if [ -d frontend/admin-dashboard ]; then cd frontend/admin-dashboard && yarn prettier --write src/; fi

lint:
	@echo "🔍 Linting code..."
	@if [ -d frontend/admin-dashboard ]; then cd frontend/admin-dashboard && yarn lint; fi
	@if [ -d frontend/driver-app ]; then cd frontend/driver-app && yarn lint; fi
