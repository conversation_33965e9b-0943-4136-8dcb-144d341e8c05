# TECNO DRIVE - Git Ignore File

# Operating System Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# IDE and Editor Files
.vscode/settings.json
.vscode/launch.json
.vscode/extensions.json
.idea/
*.swp
*.swo
*~

# Java/Maven
target/
!.mvn/wrapper/maven-wrapper.jar
!**/src/main/**/target/
!**/src/test/**/target/
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.properties

# Spring Boot
spring-boot-*.log
application-*.properties
application-*.yml
!application.properties
!application.yml
!application-*.properties.example
!application-*.yml.example

# Node.js/React
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output

# ESLint cache
.eslintcache

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# React Build
build/
dist/

# Environment Variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# Database
*.db
*.sqlite
*.sqlite3
database/backups/*.sql
!database/backups/.gitkeep

# Docker
.docker/
docker-compose.override.yml
.dockerignore

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl
terraform.tfvars
*.tfvars

# Kubernetes
*.kubeconfig

# Monitoring and Metrics
prometheus_data/
grafana_data/

# Testing
coverage/
.coverage
htmlcov/
.pytest_cache/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
pip-log.txt
pip-delete-this-directory.txt

# Certificates and Keys
*.pem
*.key
*.crt
*.csr
*.p12
*.pfx
secrets/
certs/

# Backup files
*.bak
*.backup
*.old
*.orig

# Temporary files
*.tmp
*.temp
*~

# Archive files
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Local configuration files
config/local.json
config/local.yml
config/local.yaml

# Development tools
.vscode/chrome-debug-profile*/
.vscode/settings.json

# Package managers
package-lock.json
yarn.lock
pnpm-lock.yaml

# Build artifacts
target/
build/
dist/
out/

# Cache directories
.cache/
.parcel-cache/
.next/
.nuxt/

# Editor directories and files
.vscode/*
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
tecno-drive-*.log
*.pid
.env.*.local
