package com.tecnodrive.analytics.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RouteOptimizationRequest {
    private String origin;
    private String destination;
    private String vehicleType;
    private String optimizationType; // FASTEST, SHORTEST, ECO_FRIENDLY
    private Boolean avoidTolls;
    private Boolean avoidHighways;
}
