package com.tecnodrive.location.service;

import com.google.maps.DirectionsApi;
import com.google.maps.GeoApiContext;
import com.google.maps.GeocodingApi;
import com.google.maps.model.*;
import com.tecnodrive.location.dto.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Maps Integration Service
 * Integrates with Google Maps, OpenStreetMap, and Mapbox APIs
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MapsIntegrationService {

    @Value("${google.maps.api.key}")
    private String googleMapsApiKey;

    @Value("${mapbox.access.token}")
    private String mapboxAccessToken;

    private GeoApiContext geoApiContext;

    public void initializeGoogleMaps() {
        if (geoApiContext == null) {
            geoApiContext = new GeoApiContext.Builder()
                    .apiKey(googleMapsApiKey)
                    .connectTimeout(10, TimeUnit.SECONDS)
                    .readTimeout(10, TimeUnit.SECONDS)
                    .writeTimeout(10, TimeUnit.SECONDS)
                    .build();
        }
    }

    /**
     * Geocode address to coordinates
     */
    public LocationCoordinates geocodeAddress(String address) {
        try {
            initializeGoogleMaps();
            
            GeocodingResult[] results = GeocodingApi.geocode(geoApiContext, address).await();
            
            if (results.length > 0) {
                LatLng location = results[0].geometry.location;
                return LocationCoordinates.builder()
                        .latitude(location.lat)
                        .longitude(location.lng)
                        .address(results[0].formattedAddress)
                        .placeId(results[0].placeId)
                        .build();
            }
            
            throw new LocationNotFoundException("Address not found: " + address);
            
        } catch (Exception e) {
            log.error("Error geocoding address: {}", address, e);
            throw new MapsIntegrationException("Failed to geocode address", e);
        }
    }

    /**
     * Reverse geocode coordinates to address
     */
    public String reverseGeocode(double latitude, double longitude) {
        try {
            initializeGoogleMaps();
            
            LatLng location = new LatLng(latitude, longitude);
            GeocodingResult[] results = GeocodingApi.reverseGeocode(geoApiContext, location).await();
            
            if (results.length > 0) {
                return results[0].formattedAddress;
            }
            
            return "Unknown Location";
            
        } catch (Exception e) {
            log.error("Error reverse geocoding coordinates: {}, {}", latitude, longitude, e);
            return "Unknown Location";
        }
    }

    /**
     * Calculate optimal route between two points
     */
    public RouteInfo calculateRoute(LocationCoordinates origin, LocationCoordinates destination, 
                                  RouteOptimization optimization) {
        try {
            initializeGoogleMaps();
            
            LatLng originLatLng = new LatLng(origin.getLatitude(), origin.getLongitude());
            LatLng destinationLatLng = new LatLng(destination.getLatitude(), destination.getLongitude());
            
            var request = DirectionsApi.newRequest(geoApiContext)
                    .origin(originLatLng)
                    .destination(destinationLatLng)
                    .mode(TravelMode.DRIVING)
                    .alternatives(true)
                    .units(Unit.METRIC);
            
            // Apply optimization preferences
            switch (optimization) {
                case FASTEST:
                    request.optimizeWaypoints(true);
                    break;
                case SHORTEST:
                    request.avoid(DirectionsApi.RouteRestriction.HIGHWAYS);
                    break;
                case ECO_FRIENDLY:
                    request.avoid(DirectionsApi.RouteRestriction.HIGHWAYS, DirectionsApi.RouteRestriction.TOLLS);
                    break;
            }
            
            DirectionsResult result = request.await();
            
            if (result.routes.length > 0) {
                DirectionsRoute route = result.routes[0];
                DirectionsLeg leg = route.legs[0];
                
                return RouteInfo.builder()
                        .distanceMeters(leg.distance.inMeters)
                        .durationSeconds(leg.duration.inSeconds)
                        .polyline(route.overviewPolyline.getEncodedPath())
                        .steps(convertSteps(leg.steps))
                        .trafficDuration(leg.durationInTraffic != null ? leg.durationInTraffic.inSeconds : null)
                        .build();
            }
            
            throw new RouteNotFoundException("No route found between the specified points");
            
        } catch (Exception e) {
            log.error("Error calculating route", e);
            throw new MapsIntegrationException("Failed to calculate route", e);
        }
    }

    /**
     * Calculate route with multiple waypoints
     */
    public RouteInfo calculateMultiWaypointRoute(LocationCoordinates origin, 
                                               LocationCoordinates destination,
                                               List<LocationCoordinates> waypoints) {
        try {
            initializeGoogleMaps();
            
            LatLng originLatLng = new LatLng(origin.getLatitude(), origin.getLongitude());
            LatLng destinationLatLng = new LatLng(destination.getLatitude(), destination.getLongitude());
            
            LatLng[] waypointArray = waypoints.stream()
                    .map(wp -> new LatLng(wp.getLatitude(), wp.getLongitude()))
                    .toArray(LatLng[]::new);
            
            DirectionsResult result = DirectionsApi.newRequest(geoApiContext)
                    .origin(originLatLng)
                    .destination(destinationLatLng)
                    .waypoints(waypointArray)
                    .optimizeWaypoints(true)
                    .mode(TravelMode.DRIVING)
                    .units(Unit.METRIC)
                    .await();
            
            if (result.routes.length > 0) {
                DirectionsRoute route = result.routes[0];
                
                long totalDistance = 0;
                long totalDuration = 0;
                List<RouteStep> allSteps = new ArrayList<>();
                
                for (DirectionsLeg leg : route.legs) {
                    totalDistance += leg.distance.inMeters;
                    totalDuration += leg.duration.inSeconds;
                    allSteps.addAll(convertSteps(leg.steps));
                }
                
                return RouteInfo.builder()
                        .distanceMeters(totalDistance)
                        .durationSeconds(totalDuration)
                        .polyline(route.overviewPolyline.getEncodedPath())
                        .steps(allSteps)
                        .waypointOrder(result.routes[0].waypointOrder)
                        .build();
            }
            
            throw new RouteNotFoundException("No route found for the specified waypoints");
            
        } catch (Exception e) {
            log.error("Error calculating multi-waypoint route", e);
            throw new MapsIntegrationException("Failed to calculate multi-waypoint route", e);
        }
    }

    /**
     * Get real-time traffic information
     */
    public TrafficInfo getTrafficInfo(LocationCoordinates origin, LocationCoordinates destination) {
        try {
            initializeGoogleMaps();
            
            LatLng originLatLng = new LatLng(origin.getLatitude(), origin.getLongitude());
            LatLng destinationLatLng = new LatLng(destination.getLatitude(), destination.getLongitude());
            
            DirectionsResult result = DirectionsApi.newRequest(geoApiContext)
                    .origin(originLatLng)
                    .destination(destinationLatLng)
                    .mode(TravelMode.DRIVING)
                    .departureTime(java.time.Instant.now())
                    .trafficModel(TrafficModel.BEST_GUESS)
                    .await();
            
            if (result.routes.length > 0) {
                DirectionsLeg leg = result.routes[0].legs[0];
                
                long normalDuration = leg.duration.inSeconds;
                long trafficDuration = leg.durationInTraffic != null ? 
                        leg.durationInTraffic.inSeconds : normalDuration;
                
                double trafficFactor = (double) trafficDuration / normalDuration;
                TrafficLevel trafficLevel = determineTrafficLevel(trafficFactor);
                
                return TrafficInfo.builder()
                        .normalDurationSeconds(normalDuration)
                        .trafficDurationSeconds(trafficDuration)
                        .trafficFactor(trafficFactor)
                        .trafficLevel(trafficLevel)
                        .lastUpdated(java.time.LocalDateTime.now())
                        .build();
            }
            
            throw new TrafficInfoNotFoundException("Traffic information not available");
            
        } catch (Exception e) {
            log.error("Error getting traffic info", e);
            throw new MapsIntegrationException("Failed to get traffic information", e);
        }
    }

    private List<RouteStep> convertSteps(DirectionsStep[] steps) {
        List<RouteStep> routeSteps = new ArrayList<>();
        
        for (DirectionsStep step : steps) {
            RouteStep routeStep = RouteStep.builder()
                    .instruction(step.htmlInstructions)
                    .distanceMeters(step.distance.inMeters)
                    .durationSeconds(step.duration.inSeconds)
                    .startLocation(LocationCoordinates.builder()
                            .latitude(step.startLocation.lat)
                            .longitude(step.startLocation.lng)
                            .build())
                    .endLocation(LocationCoordinates.builder()
                            .latitude(step.endLocation.lat)
                            .longitude(step.endLocation.lng)
                            .build())
                    .polyline(step.polyline.getEncodedPath())
                    .build();
            
            routeSteps.add(routeStep);
        }
        
        return routeSteps;
    }

    private TrafficLevel determineTrafficLevel(double trafficFactor) {
        if (trafficFactor <= 1.1) return TrafficLevel.LIGHT;
        if (trafficFactor <= 1.3) return TrafficLevel.MODERATE;
        if (trafficFactor <= 1.6) return TrafficLevel.HEAVY;
        return TrafficLevel.SEVERE;
    }

    // DTOs and Enums
    public enum RouteOptimization {
        FASTEST, SHORTEST, ECO_FRIENDLY
    }

    public enum TrafficLevel {
        LIGHT, MODERATE, HEAVY, SEVERE
    }

    // Exception classes
    public static class LocationNotFoundException extends RuntimeException {
        public LocationNotFoundException(String message) {
            super(message);
        }
    }

    public static class RouteNotFoundException extends RuntimeException {
        public RouteNotFoundException(String message) {
            super(message);
        }
    }

    public static class TrafficInfoNotFoundException extends RuntimeException {
        public TrafficInfoNotFoundException(String message) {
            super(message);
        }
    }

    public static class MapsIntegrationException extends RuntimeException {
        public MapsIntegrationException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
