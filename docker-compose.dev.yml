version: '3.8'

services:
  # Development Database
  postgres-dev:
    image: postgres:15-alpine
    container_name: tecnodrive-postgres-dev
    environment:
      POSTGRES_DB: tecnodrive_dev
      POSTGRES_USER: dev
      POSTGRES_PASSWORD: dev123
    ports:
      - "5433:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./database/dev-init:/docker-entrypoint-initdb.d
    networks:
      - tecnodrive-dev-network

  # Development Redis
  redis-dev:
    image: redis:7-alpine
    container_name: tecnodrive-redis-dev
    ports:
      - "6380:6379"
    volumes:
      - redis_dev_data:/data
    networks:
      - tecnodrive-dev-network

  # Development RabbitMQ
  rabbitmq-dev:
    image: rabbitmq:3-management-alpine
    container_name: tecnodrive-rabbitmq-dev
    environment:
      RABBITMQ_DEFAULT_USER: dev
      RABBITMQ_DEFAULT_PASS: dev123
    ports:
      - "5673:5672"
      - "15673:15672"
    volumes:
      - rabbitmq_dev_data:/var/lib/rabbitmq
    networks:
      - tecnodrive-dev-network

  # Development Tools
  mailhog:
    image: mailhog/mailhog:latest
    container_name: tecnodrive-mailhog
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - tecnodrive-dev-network

  # Database Admin Tool
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: tecnodrive-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - tecnodrive-dev-network

  # Redis Admin Tool
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: tecnodrive-redis-commander
    environment:
      REDIS_HOSTS: local:redis-dev:6379
    ports:
      - "8081:8081"
    networks:
      - tecnodrive-dev-network

volumes:
  postgres_dev_data:
  redis_dev_data:
  rabbitmq_dev_data:
  pgadmin_data:

networks:
  tecnodrive-dev-network:
    driver: bridge
