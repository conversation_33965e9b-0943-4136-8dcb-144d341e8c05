<?xml version="1.0" encoding="UTF-8"?>

<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<module version="4" relativePaths="false" type="JAVA_MODULE"> 
  <component name="NewModuleRootManager"> 
    <exclude-output/>  
    <orderEntry type="inheritedJdk"/>  
    <!-- output url="file://$$MODULE_DIR$$/${maven.build.dest}"/ -->  
    <!-- output-test url="file://$$MODULE_DIR$$/${maven.test.dest}"/ -->  
    <content url="file://$MODULE_DIR$"> 
      <!-- sourceFolder url="file://$$MODULE_DIR$$/${pom.build.sourceDirectory}" isTestSource="false"/ -->  
      <!-- sourceFolder url="file://$$MODULE_DIR$$/${pom.build.testSourceDirectory}" isTestSource="true"/ -->  
      <sourceFolder url="file://$MODULE_DIR$/src/main/java" isTestSource="false"/>
      <excludeFolder url="file://$MODULE_DIR$/target"/>
    </content>  
    <orderEntry type="sourceFolder" forTests="false"/>  
    <!-- Next include each dependency:
      <orderEntry type="module" module-name="${dep.artifactId}"/>
      <orderEntry type="module-library">
        <library name="${dep.artifactId}">
          <CLASSES>
            <root url="jar://${lib.path}!/"/>
          </CLASSES>
          <JAVADOC/>
          <SOURCES/>
        </library>
      </orderEntry>
     -->  
    <output url="file://$MODULE_DIR$/target/classes"/>  
    <output-test url="file://$MODULE_DIR$/target/test-classes"/>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/3.2.0/spring-boot-starter-websocket-3.2.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.2.0/spring-boot-starter-web-3.2.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.2.0/spring-boot-starter-3.2.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.2.0/spring-boot-3.2.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.1.1/spring-core-6.1.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.1.1/spring-jcl-6.1.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/spring-context/6.1.1/spring-context-6.1.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.1.1/spring-aop-6.1.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.1.1/spring-beans-6.1.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.1.1/spring-expression-6.1.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.12.0/micrometer-observation-1.12.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.12.0/micrometer-commons-1.12.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.2.0/spring-boot-autoconfigure-3.2.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.2.0/spring-boot-starter-logging-3.2.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.4.11/logback-classic-1.4.11.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.4.11/logback-core-1.4.11.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.9/slf4j-api-2.0.9.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.21.1/log4j-to-slf4j-2.21.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.21.1/log4j-api-2.21.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.9/jul-to-slf4j-2.0.9.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.2/snakeyaml-2.2.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.2.0/spring-boot-starter-json-3.2.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.1.1/spring-web-6.1.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.15.3/jackson-databind-2.15.3.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.15.3/jackson-annotations-2.15.3.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.15.3/jackson-core-2.15.3.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.15.3/jackson-datatype-jdk8-2.15.3.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.15.3/jackson-datatype-jsr310-2.15.3.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.15.3/jackson-module-parameter-names-2.15.3.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/3.2.0/spring-boot-starter-tomcat-3.2.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.16/tomcat-embed-core-10.1.16.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.16/tomcat-embed-el-10.1.16.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.16/tomcat-embed-websocket-10.1.16.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.1.1/spring-webmvc-6.1.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/6.1.1/spring-messaging-6.1.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/6.1.1/spring-websocket-6.1.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jpa/3.2.0/spring-boot-starter-data-jpa-3.2.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/3.2.0/spring-boot-starter-aop-3.2.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.20.1/aspectjweaver-1.9.20.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/3.2.0/spring-boot-starter-jdbc-3.2.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/5.0.1/HikariCP-5.0.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/6.1.1/spring-jdbc-6.1.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.1.1/spring-tx-6.1.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/hibernate/orm/hibernate-core/6.3.1.Final/hibernate-core-6.3.1.Final.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/jakarta/persistence/jakarta.persistence-api/3.1.0/jakarta.persistence-api-3.1.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/jakarta/transaction/jakarta.transaction-api/2.0.1/jakarta.transaction-api-2.0.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.5.3.Final/jboss-logging-3.5.3.Final.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/hibernate/common/hibernate-commons-annotations/6.0.6.Final/hibernate-commons-annotations-6.0.6.Final.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/io/smallrye/jandex/3.1.2/jandex-3.1.2.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.6.0/classmate-1.6.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.14.10/byte-buddy-1.14.10.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.1/jakarta.xml.bind-api-4.0.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.2/jakarta.activation-api-2.1.2.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-runtime/4.0.4/jaxb-runtime-4.0.4.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-core/4.0.4/jaxb-core-4.0.4.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/eclipse/angus/angus-activation/2.0.1/angus-activation-2.0.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/glassfish/jaxb/txw2/4.0.4/txw2-4.0.4.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/com/sun/istack/istack-commons-runtime/4.1.2/istack-commons-runtime-4.1.2.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-api-2.0.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.10.1/antlr4-runtime-4.10.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-jpa/3.2.0/spring-data-jpa-3.2.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/3.2.0/spring-data-commons-3.2.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/spring-orm/6.1.1/spring-orm-6.1.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/spring-aspects/6.1.1/spring-aspects-6.1.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/3.2.0/spring-boot-starter-data-redis-3.2.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/io/lettuce/lettuce-core/6.3.0.RELEASE/lettuce-core-6.3.0.RELEASE.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.101.Final/netty-common-4.1.101.Final.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.101.Final/netty-handler-4.1.101.Final.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.101.Final/netty-resolver-4.1.101.Final.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.101.Final/netty-buffer-4.1.101.Final.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.101.Final/netty-transport-4.1.101.Final.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.101.Final/netty-transport-native-unix-common-4.1.101.Final.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.101.Final/netty-codec-4.1.101.Final.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.6.0/reactor-core-3.6.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/3.2.0/spring-data-redis-3.2.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/3.2.0/spring-data-keyvalue-3.2.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/6.1.1/spring-oxm-6.1.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/6.1.1/spring-context-support-6.1.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/postgresql/postgresql/42.6.0/postgresql-42.6.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.31.0/checker-qual-3.31.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.5.5.Final/mapstruct-1.5.5.Final.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/3.2.0/spring-boot-starter-test-3.2.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/3.2.0/spring-boot-test-3.2.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/3.2.0/spring-boot-test-autoconfigure-3.2.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.8.0/json-path-2.8.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.5.0/json-smart-2.5.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.5.0/accessors-smart-2.5.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/ow2/asm/asm/9.3/asm-9.3.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.24.2/assertj-core-3.24.2.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.0/awaitility-4.2.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.10.1/junit-jupiter-5.10.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.10.1/junit-jupiter-api-5.10.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.10.1/junit-platform-commons-1.10.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.10.1/junit-jupiter-params-5.10.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.10.1/junit-jupiter-engine-5.10.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.10.1/junit-platform-engine-1.10.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/mockito/mockito-core/5.7.0/mockito-core-5.7.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.14.10/byte-buddy-agent-1.14.10.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.7.0/mockito-junit-jupiter-5.7.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.1/jsonassert-1.5.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/spring-test/6.1.1/spring-test-6.1.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.9.1/xmlunit-core-2.9.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-webmvc-ui/2.2.0/springdoc-openapi-starter-webmvc-ui-2.2.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-webmvc-api/2.2.0/springdoc-openapi-starter-webmvc-api-2.2.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-common/2.2.0/springdoc-openapi-starter-common-2.2.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-core-jakarta/2.2.15/swagger-core-jakarta-2.2.15.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.13.0/commons-lang3-3.13.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-annotations-jakarta/2.2.15/swagger-annotations-jakarta-2.2.15.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-models-jakarta/2.2.15/swagger-models-jakarta-2.2.15.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.15.3/jackson-dataformat-yaml-2.15.3.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/webjars/swagger-ui/5.2.0/swagger-ui-5.2.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-starter-netflix-eureka-client/4.1.0/spring-cloud-starter-netflix-eureka-client-4.1.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-starter/4.1.0/spring-cloud-starter-4.1.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-context/4.1.0/spring-cloud-context-4.1.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/6.2.0/spring-security-crypto-6.2.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-commons/4.1.0/spring-cloud-commons-4.1.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-rsa/1.1.1/spring-security-rsa-1.1.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk18on/1.74/bcprov-jdk18on-1.74.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-netflix-eureka-client/4.1.0/spring-cloud-netflix-eureka-client-4.1.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/com/netflix/eureka/eureka-client/2.0.1/eureka-client-2.0.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/com/netflix/netflix-commons/netflix-eventbus/0.3.0/netflix-eventbus-0.3.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/com/netflix/netflix-commons/netflix-infix/0.3.0/netflix-infix-0.3.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/commons-jxpath/commons-jxpath/1.3/commons-jxpath-1.3.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/joda-time/joda-time/2.3/joda-time-2.3.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/antlr/antlr-runtime/3.4/antlr-runtime-3.4.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/antlr/stringtemplate/3.2.1/stringtemplate-3.2.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/antlr/antlr/2.7.7/antlr-2.7.7.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/com/google/guava/guava/16.0/guava-16.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.10.1/gson-2.10.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/com/netflix/servo/servo-core/0.12.21/servo-core-0.12.21.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/apache/commons/commons-math/2.2/commons-math-2.2.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/com/thoughtworks/xstream/xstream/1.4.19/xstream-1.4.19.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/io/github/x-stream/mxparser/1.2.2/mxparser-1.2.2.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/xmlpull/xmlpull/1.1.3.1/xmlpull-1.1.3.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/jakarta/ws/rs/jakarta.ws.rs-api/3.1.0/jakarta.ws.rs-api-3.1.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.3/httpclient-4.5.3.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.16.0/commons-codec-1.16.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/commons-configuration/commons-configuration/1.10/commons-configuration-1.10.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/codehaus/jettison/jettison/1.4.0/jettison-1.4.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/apache/httpcomponents/client5/httpclient5/5.2.1/httpclient5-5.2.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/apache/httpcomponents/core5/httpcore5/5.2.3/httpcore5-5.2.3.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/apache/httpcomponents/core5/httpcore5-h2/5.2.3/httpcore5-h2-5.2.3.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/javax/servlet/servlet-api/2.5/servlet-api-2.5.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/com/netflix/archaius/archaius-core/0.7.6/archaius-core-0.7.6.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/com/github/vlsi/compactmap/compactmap/2.0/compactmap-2.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/com/github/andrewoma/dexx/dexx-collections/0.2/dexx-collections-0.2.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/com/netflix/eureka/eureka-core/2.0.1/eureka-core-2.0.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/com/fasterxml/woodstox/woodstox-core/6.2.1/woodstox-core-6.2.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/codehaus/woodstox/stax2-api/4.2.1/stax2-api-4.2.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-starter-loadbalancer/4.1.0/spring-cloud-starter-loadbalancer-4.1.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/cloud/spring-cloud-loadbalancer/4.1.0/spring-cloud-loadbalancer-4.1.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/io/projectreactor/addons/reactor-extra/3.5.1/reactor-extra-3.5.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-cache/3.2.0/spring-boot-starter-cache-3.2.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/com/stoyanr/evictor/1.0.0/evictor-1.0.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://C:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.30/lombok-1.18.30.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry> 
  </component>  
  <component name="ModuleRootManager"/>  
  <!-- If it's a war project:
  <component name="WebModuleProperties">
    <containerElement type="module" name="${dep.artifactId}">
      <attribute name="method" value="1" />
      <attribute name="URI" value="/WEB-INF/classes" />
    </containerElement>
    <containerElement type="library" level="module" name="${dep.artifactId}">
      <attribute name="method" value="1" />
      <attribute name="URI" value="/WEB-INF/lib/${dep.systemPath.name}" />
    </containerElement>
    <deploymentDescriptor name="web.xml" url="file://$MODULE_DIR$/${pom.build.warSourceDirectory}/WEB-INF/web.xml" version="" />
    <webroots>
      <root url="file://$MODULE_DIR$/${pom.build.warSourceDirectory}" relative="/" />
    </webroots>
  </component>
  --> 
</module>
