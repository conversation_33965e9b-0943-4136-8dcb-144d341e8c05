# TECNO DRIVE Environment Configuration
# Copy this file to .env and update the values

# Database Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=tecnodrive
POSTGRES_USER=admin
POSTGRES_PASSWORD=secret

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# RabbitMQ Configuration
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USER=admin
RABBITMQ_PASSWORD=secret

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRATION=86400

# OAuth2 Configuration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
FACEBOOK_CLIENT_ID=your-facebook-client-id
FACEBOOK_CLIENT_SECRET=your-facebook-client-secret

# Payment Gateway Configuration
STRIPE_PUBLIC_KEY=pk_test_your-stripe-public-key
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret

# Maps API Configuration
GOOGLE_MAPS_API_KEY=your-google-maps-api-key
MAPBOX_ACCESS_TOKEN=your-mapbox-access-token

# Notification Services
FIREBASE_SERVER_KEY=your-firebase-server-key
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
SENDGRID_API_KEY=your-sendgrid-api-key

# Monitoring Configuration
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000
GRAFANA_ADMIN_PASSWORD=admin

# Application Ports
API_GATEWAY_PORT=8080
AUTH_SERVICE_PORT=8081
USER_SERVICE_PORT=8082
RIDE_SERVICE_PORT=8083
FLEET_SERVICE_PORT=8084
PAYMENT_SERVICE_PORT=8085
LOCATION_SERVICE_PORT=8086
NOTIFICATION_SERVICE_PORT=8087
ANALYTICS_SERVICE_PORT=8088
PARCEL_SERVICE_PORT=8089

# Frontend Ports
ADMIN_DASHBOARD_PORT=3000
DRIVER_APP_PORT=3001
PASSENGER_APP_PORT=3002
OPERATOR_DASHBOARD_PORT=4200

# Development Configuration
SPRING_PROFILES_ACTIVE=dev
NODE_ENV=development
DEBUG=true

# File Upload Configuration
MAX_FILE_SIZE=10MB
UPLOAD_PATH=./uploads

# Rate Limiting
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json

# Security Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:3002,http://localhost:4200
ALLOWED_HOSTS=localhost,127.0.0.1

# AI/ML Configuration
TENSORFLOW_MODEL_PATH=./models
SPARK_MASTER_URL=local[*]
ML_PREDICTION_THRESHOLD=0.8
