package com.tecnodrive.auth.service;

import com.tecnodrive.auth.dto.LoginRequest;
import com.tecnodrive.auth.dto.LoginResponse;
import com.tecnodrive.auth.dto.RegisterRequest;
import com.tecnodrive.auth.dto.TokenRefreshRequest;
import com.tecnodrive.auth.entity.RefreshToken;
import com.tecnodrive.auth.entity.User;
import com.tecnodrive.auth.exception.AuthenticationException;
import com.tecnodrive.auth.exception.UserAlreadyExistsException;
import com.tecnodrive.auth.exception.InvalidTokenException;
import com.tecnodrive.auth.repository.RefreshTokenRepository;
import com.tecnodrive.auth.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Set;
import java.util.UUID;

/**
 * Authentication Service
 * Handles user authentication, registration, and token management
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class AuthService {

    private final UserRepository userRepository;
    private final RefreshTokenRepository refreshTokenRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtService jwtService;
    private final RedisService redisService;

    private static final int MAX_LOGIN_ATTEMPTS = 5;
    private static final int ACCOUNT_LOCK_DURATION_MINUTES = 30;

    /**
     * Register new user
     */
    public LoginResponse register(RegisterRequest request) {
        log.info("Registering new user with email: {}", request.getEmail());

        // Check if user already exists
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new UserAlreadyExistsException("User with email already exists: " + request.getEmail());
        }

        if (request.getPhoneNumber() != null && userRepository.existsByPhoneNumber(request.getPhoneNumber())) {
            throw new UserAlreadyExistsException("User with phone number already exists: " + request.getPhoneNumber());
        }

        // Create new user
        User user = User.builder()
                .email(request.getEmail())
                .phoneNumber(request.getPhoneNumber())
                .passwordHash(passwordEncoder.encode(request.getPassword()))
                .firstName(request.getFirstName())
                .lastName(request.getLastName())
                .status(User.UserStatus.ACTIVE)
                .roles(Set.of(User.UserRole.PASSENGER)) // Default role
                .emailVerified(false)
                .phoneVerified(false)
                .twoFactorEnabled(false)
                .failedLoginAttempts(0)
                .build();

        user = userRepository.save(user);
        log.info("User registered successfully with ID: {}", user.getId());

        // Generate tokens
        return generateTokenResponse(user, request.getDeviceInfo(), request.getIpAddress());
    }

    /**
     * Authenticate user login
     */
    public LoginResponse login(LoginRequest request) {
        log.info("Login attempt for identifier: {}", request.getIdentifier());

        // Find user by email or phone
        User user = userRepository.findByEmailOrPhoneNumber(request.getIdentifier())
                .orElseThrow(() -> new AuthenticationException("Invalid credentials"));

        // Check if account is locked
        if (user.isAccountLocked()) {
            throw new AuthenticationException("Account is temporarily locked. Please try again later.");
        }

        // Verify password
        if (!passwordEncoder.matches(request.getPassword(), user.getPasswordHash())) {
            handleFailedLogin(user);
            throw new AuthenticationException("Invalid credentials");
        }

        // Check if account is active
        if (!user.isAccountActive()) {
            throw new AuthenticationException("Account is not active");
        }

        // Reset failed login attempts and update last login
        userRepository.updateFailedLoginAttempts(user.getId(), 0);
        userRepository.updateLastLoginTime(user.getId(), LocalDateTime.now());

        log.info("User logged in successfully: {}", user.getId());

        // Generate tokens
        return generateTokenResponse(user, request.getDeviceInfo(), request.getIpAddress());
    }

    /**
     * Refresh access token
     */
    public LoginResponse refreshToken(TokenRefreshRequest request) {
        log.info("Refreshing token");

        RefreshToken refreshToken = refreshTokenRepository.findByToken(request.getRefreshToken())
                .orElseThrow(() -> new InvalidTokenException("Invalid refresh token"));

        if (!refreshToken.isValid()) {
            refreshTokenRepository.revokeToken(request.getRefreshToken());
            throw new InvalidTokenException("Refresh token is expired or revoked");
        }

        User user = refreshToken.getUser();
        
        // Generate new tokens
        String newAccessToken = jwtService.generateAccessToken(user);
        String newRefreshToken = jwtService.generateRefreshToken(user);

        // Revoke old refresh token
        refreshTokenRepository.revokeToken(request.getRefreshToken());

        // Save new refresh token
        saveRefreshToken(user, newRefreshToken, request.getDeviceInfo(), request.getIpAddress());

        return LoginResponse.builder()
                .accessToken(newAccessToken)
                .refreshToken(newRefreshToken)
                .tokenType("Bearer")
                .expiresIn(jwtService.getAccessTokenExpiration())
                .user(mapUserToResponse(user))
                .build();
    }

    /**
     * Logout user
     */
    public void logout(String refreshToken) {
        log.info("Logging out user");
        
        if (refreshToken != null) {
            refreshTokenRepository.revokeToken(refreshToken);
        }
    }

    /**
     * Logout from all devices
     */
    public void logoutFromAllDevices(UUID userId) {
        log.info("Logging out user from all devices: {}", userId);
        
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new AuthenticationException("User not found"));
        
        refreshTokenRepository.revokeAllTokensForUser(user);
    }

    /**
     * Generate token response
     */
    private LoginResponse generateTokenResponse(User user, String deviceInfo, String ipAddress) {
        String accessToken = jwtService.generateAccessToken(user);
        String refreshToken = jwtService.generateRefreshToken(user);

        // Save refresh token
        saveRefreshToken(user, refreshToken, deviceInfo, ipAddress);

        return LoginResponse.builder()
                .accessToken(accessToken)
                .refreshToken(refreshToken)
                .tokenType("Bearer")
                .expiresIn(jwtService.getAccessTokenExpiration())
                .user(mapUserToResponse(user))
                .build();
    }

    /**
     * Save refresh token to database
     */
    private void saveRefreshToken(User user, String token, String deviceInfo, String ipAddress) {
        RefreshToken refreshToken = RefreshToken.builder()
                .token(token)
                .user(user)
                .expiresAt(jwtService.getExpirationFromToken(token))
                .deviceInfo(deviceInfo)
                .ipAddress(ipAddress)
                .revoked(false)
                .build();

        refreshTokenRepository.save(refreshToken);
    }

    /**
     * Handle failed login attempt
     */
    private void handleFailedLogin(User user) {
        int attempts = user.getFailedLoginAttempts() + 1;
        userRepository.updateFailedLoginAttempts(user.getId(), attempts);

        if (attempts >= MAX_LOGIN_ATTEMPTS) {
            LocalDateTime lockUntil = LocalDateTime.now().plusMinutes(ACCOUNT_LOCK_DURATION_MINUTES);
            userRepository.lockUserAccount(user.getId(), lockUntil);

            // Cache the lock status in Redis for faster access
            redisService.setValue("user:locked:" + user.getId(), "true", Duration.ofMinutes(ACCOUNT_LOCK_DURATION_MINUTES));

            log.warn("Account locked for user: {} due to {} failed login attempts", user.getId(), attempts);
        }
    }

    /**
     * Map user entity to response DTO
     */
    private LoginResponse.UserInfo mapUserToResponse(User user) {
        return LoginResponse.UserInfo.builder()
                .id(user.getId())
                .email(user.getEmail())
                .firstName(user.getFirstName())
                .lastName(user.getLastName())
                .profileImageUrl(user.getProfileImageUrl())
                .roles(user.getRoles())
                .emailVerified(user.getEmailVerified())
                .phoneVerified(user.getPhoneVerified())
                .build();
    }
}
