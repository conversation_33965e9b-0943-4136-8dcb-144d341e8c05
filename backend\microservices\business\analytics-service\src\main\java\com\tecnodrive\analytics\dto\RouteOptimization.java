package com.tecnodrive.analytics.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RouteOptimization {
    private String recommendedRoute;
    private Double estimatedDuration;
    private Double estimatedDistance;
    private Double trafficScore;
    private Double fuelEfficiencyScore;
    private Double confidence;
    private Map<String, Object> optimizationFactors;
    private LocalDateTime generatedAt;
}
