@echo off
echo Refreshing Maven Projects Configuration...

echo Step 1: Cleaning all projects...
mvn clean -q

echo Step 2: Compiling all projects...
mvn compile -q

echo Step 3: Generating IDE project files...
mvn idea:idea -q

echo Maven projects refreshed successfully!
echo.
echo Next steps:
echo 1. In IntelliJ IDEA: Open Maven tool window and click Reimport All Maven Projects
echo 2. Or use Ctrl+Shift+O to reimport
echo 3. If issues persist, restart your IDE
