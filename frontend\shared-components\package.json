{"name": "@tecnodrive/shared-components", "version": "1.0.0", "description": "Shared UI Components for TECNO DRIVE Applications", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "jest", "lint": "eslint src --ext .ts,.tsx", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "styled-components": "^6.1.0", "framer-motion": "^10.16.0", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/material": "^5.14.0", "@mui/icons-material": "^5.14.0", "react-hook-form": "^7.47.0", "react-query": "^3.39.3"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/styled-components": "^5.1.0", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "@storybook/react": "^7.5.0", "@storybook/addon-essentials": "^7.5.0", "eslint": "^8.50.0", "eslint-plugin-react": "^7.33.2", "jest": "^29.7.0", "typescript": "^5.2.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "keywords": ["react", "components", "ui", "design-system", "tecnodrive"], "author": "TECNO DRIVE Team", "license": "MIT", "files": ["dist"]}