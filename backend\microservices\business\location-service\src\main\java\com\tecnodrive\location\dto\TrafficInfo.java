package com.tecnodrive.location.dto;

import com.tecnodrive.location.service.MapsIntegrationService.TrafficLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrafficInfo {
    private Long normalDurationSeconds;
    private Long trafficDurationSeconds;
    private Double trafficFactor;
    private TrafficLevel trafficLevel;
    private LocalDateTime lastUpdated;
}
