package com.tecnodrive.location.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RouteInfo {
    private Long distanceMeters;
    private Long durationSeconds;
    private String polyline;
    private List<RouteStep> steps;
    private Long trafficDuration;
    private int[] waypointOrder;
}
