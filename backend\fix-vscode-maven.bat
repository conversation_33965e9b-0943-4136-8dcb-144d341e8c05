@echo off
echo ========================================
echo   TECNO DRIVE - VS Code Maven Fix
echo ========================================
echo.

echo [1/5] Cleaning Maven projects...
mvn clean -q
if %errorlevel% neq 0 (
    echo ERROR: Maven clean failed
    pause
    exit /b 1
)

echo [2/5] Installing dependencies...
mvn install -DskipTests -q
if %errorlevel% neq 0 (
    echo ERROR: Maven install failed
    pause
    exit /b 1
)

echo [3/5] Resolving dependencies...
mvn dependency:resolve -q

echo [4/5] Generating classpath...
mvn dependency:build-classpath -q

echo [5/5] Updating project configuration...
mvn compile -q

echo.
echo ========================================
echo   SUCCESS: VS Code Maven config fixed!
echo ========================================
echo.
echo NEXT STEPS IN VS CODE:
echo.
echo 1. Open VS Code in this directory:
echo    code .
echo.
echo 2. Open Command Palette (Ctrl+Shift+P) and run:
echo    - Java: Clean Java Language Server Workspace
echo    - Maven: Update Project Configuration
echo    - Java: Reload Projects
echo.
echo 3. If issues persist:
echo    - Restart VS Code
echo    - Check Java Extension Pack is installed
echo.
echo 4. Open the workspace file:
echo    File -^> Open Workspace from File -^> tecnodrive-backend.code-workspace
echo.
pause
