{"version": "2.0.0", "tasks": [{"label": "Maven: Reload All Projects", "type": "shell", "command": "mvn", "args": ["clean", "compile", "-U"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Maven: Update Dependencies", "type": "shell", "command": "mvn", "args": ["dependency:resolve", "dependency:resolve-sources"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Fix VS Code Maven Issues", "type": "shell", "command": "mvn", "args": ["clean", "install", "-DskipTests", "-U"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}]}