package com.tecnodrive.analytics.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Maintenance Prediction DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MaintenancePrediction {
    private String vehicleId;
    private String maintenanceType;
    private String urgencyLevel;
    private double confidence;
    private List<String> riskFactors;
    private List<String> recommendations;
    private LocalDateTime predictedDate;
    private LocalDateTime generatedAt;
    private int estimatedDaysUntilMaintenance;
}
