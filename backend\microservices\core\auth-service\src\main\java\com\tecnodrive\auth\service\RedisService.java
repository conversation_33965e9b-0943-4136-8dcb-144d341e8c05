package com.tecnodrive.auth.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Set;

/**
 * Redis Service
 * Handles caching and session management using Redis
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RedisService {

    private final RedisTemplate<String, Object> redisTemplate;

    private static final String BLACKLISTED_TOKEN_PREFIX = "blacklisted_token:";
    private static final String USER_SESSION_PREFIX = "user_session:";
    private static final String LOGIN_ATTEMPTS_PREFIX = "login_attempts:";

    /**
     * Store value with expiration
     */
    public void setValue(String key, Object value, Duration expiration) {
        try {
            redisTemplate.opsForValue().set(key, value, expiration);
        } catch (Exception e) {
            log.error("Error storing value in Redis for key: {}", key, e);
        }
    }

    /**
     * Get value by key
     */
    public Object getValue(String key) {
        try {
            return redisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            log.error("Error retrieving value from Redis for key: {}", key, e);
            return null;
        }
    }

    /**
     * Delete key
     */
    public void deleteKey(String key) {
        try {
            redisTemplate.delete(key);
        } catch (Exception e) {
            log.error("Error deleting key from Redis: {}", key, e);
        }
    }

    /**
     * Check if key exists
     */
    public boolean hasKey(String key) {
        try {
            return Boolean.TRUE.equals(redisTemplate.hasKey(key));
        } catch (Exception e) {
            log.error("Error checking key existence in Redis: {}", key, e);
            return false;
        }
    }

    /**
     * Blacklist JWT token
     */
    public void blacklistToken(String token, Duration expiration) {
        String key = BLACKLISTED_TOKEN_PREFIX + token;
        setValue(key, "blacklisted", expiration);
        log.debug("Token blacklisted: {}", token.substring(0, Math.min(token.length(), 20)) + "...");
    }

    /**
     * Check if token is blacklisted
     */
    public boolean isTokenBlacklisted(String token) {
        String key = BLACKLISTED_TOKEN_PREFIX + token;
        return hasKey(key);
    }

    /**
     * Store user session
     */
    public void storeUserSession(String userId, Object sessionData, Duration expiration) {
        String key = USER_SESSION_PREFIX + userId;
        setValue(key, sessionData, expiration);
    }

    /**
     * Get user session
     */
    public Object getUserSession(String userId) {
        String key = USER_SESSION_PREFIX + userId;
        return getValue(key);
    }

    /**
     * Delete user session
     */
    public void deleteUserSession(String userId) {
        String key = USER_SESSION_PREFIX + userId;
        deleteKey(key);
    }

    /**
     * Increment login attempts
     */
    public long incrementLoginAttempts(String identifier) {
        String key = LOGIN_ATTEMPTS_PREFIX + identifier;
        try {
            Long attempts = redisTemplate.opsForValue().increment(key);
            if (attempts != null && attempts == 1) {
                // Set expiration for first attempt
                redisTemplate.expire(key, Duration.ofMinutes(30));
            }
            return attempts != null ? attempts : 0;
        } catch (Exception e) {
            log.error("Error incrementing login attempts for: {}", identifier, e);
            return 0;
        }
    }

    /**
     * Get login attempts count
     */
    public long getLoginAttempts(String identifier) {
        String key = LOGIN_ATTEMPTS_PREFIX + identifier;
        Object attempts = getValue(key);
        return attempts instanceof Number ? ((Number) attempts).longValue() : 0;
    }

    /**
     * Reset login attempts
     */
    public void resetLoginAttempts(String identifier) {
        String key = LOGIN_ATTEMPTS_PREFIX + identifier;
        deleteKey(key);
    }

    /**
     * Get all keys matching pattern
     */
    public Set<String> getKeys(String pattern) {
        try {
            return redisTemplate.keys(pattern);
        } catch (Exception e) {
            log.error("Error getting keys with pattern: {}", pattern, e);
            return Set.of();
        }
    }

    /**
     * Set expiration for existing key
     */
    public void setExpiration(String key, Duration expiration) {
        try {
            redisTemplate.expire(key, expiration);
        } catch (Exception e) {
            log.error("Error setting expiration for key: {}", key, e);
        }
    }
}
