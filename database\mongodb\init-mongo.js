// MongoDB Initialization Script for TECNO DRIVE
// Creates databases and collections for document storage

// Switch to admin database
db = db.getSiblingDB('admin');

// Create users for each service
db.createUser({
  user: 'tecnodrive_admin',
  pwd: 'mongo_admin_2024',
  roles: [
    { role: 'userAdminAnyDatabase', db: 'admin' },
    { role: 'readWriteAnyDatabase', db: 'admin' }
  ]
});

// Create TECNO DRIVE main database
db = db.getSiblingDB('tecnodrive_documents');

// Create collections for file storage and documents
db.createCollection('files', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['filename', 'contentType', 'size', 'uploadedBy', 'uploadedAt'],
      properties: {
        filename: {
          bsonType: 'string',
          description: 'Original filename'
        },
        contentType: {
          bsonType: 'string',
          description: 'MIME type of the file'
        },
        size: {
          bsonType: 'long',
          description: 'File size in bytes'
        },
        uploadedBy: {
          bsonType: 'string',
          description: 'User ID who uploaded the file'
        },
        uploadedAt: {
          bsonType: 'date',
          description: 'Upload timestamp'
        },
        tags: {
          bsonType: 'array',
          items: {
            bsonType: 'string'
          },
          description: 'File tags for categorization'
        },
        metadata: {
          bsonType: 'object',
          description: 'Additional file metadata'
        }
      }
    }
  }
});

// Create collection for driver documents
db.createCollection('driver_documents', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['driverId', 'documentType', 'status', 'submittedAt'],
      properties: {
        driverId: {
          bsonType: 'string',
          description: 'Driver UUID'
        },
        documentType: {
          bsonType: 'string',
          enum: ['license', 'insurance', 'registration', 'background_check', 'medical_certificate'],
          description: 'Type of document'
        },
        status: {
          bsonType: 'string',
          enum: ['pending', 'approved', 'rejected', 'expired'],
          description: 'Document verification status'
        },
        fileId: {
          bsonType: 'objectId',
          description: 'Reference to file in files collection'
        },
        expiryDate: {
          bsonType: 'date',
          description: 'Document expiry date'
        },
        verifiedBy: {
          bsonType: 'string',
          description: 'Admin user who verified the document'
        },
        verifiedAt: {
          bsonType: 'date',
          description: 'Verification timestamp'
        },
        rejectionReason: {
          bsonType: 'string',
          description: 'Reason for rejection if applicable'
        },
        submittedAt: {
          bsonType: 'date',
          description: 'Submission timestamp'
        }
      }
    }
  }
});

// Create collection for vehicle documents
db.createCollection('vehicle_documents', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['vehicleId', 'documentType', 'status', 'submittedAt'],
      properties: {
        vehicleId: {
          bsonType: 'string',
          description: 'Vehicle UUID'
        },
        documentType: {
          bsonType: 'string',
          enum: ['registration', 'insurance', 'inspection', 'permit'],
          description: 'Type of document'
        },
        status: {
          bsonType: 'string',
          enum: ['pending', 'approved', 'rejected', 'expired'],
          description: 'Document verification status'
        },
        fileId: {
          bsonType: 'objectId',
          description: 'Reference to file in files collection'
        },
        expiryDate: {
          bsonType: 'date',
          description: 'Document expiry date'
        },
        verifiedBy: {
          bsonType: 'string',
          description: 'Admin user who verified the document'
        },
        verifiedAt: {
          bsonType: 'date',
          description: 'Verification timestamp'
        },
        submittedAt: {
          bsonType: 'date',
          description: 'Submission timestamp'
        }
      }
    }
  }
});

// Create collection for system logs
db.createCollection('system_logs', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['service', 'level', 'message', 'timestamp'],
      properties: {
        service: {
          bsonType: 'string',
          description: 'Service name that generated the log'
        },
        level: {
          bsonType: 'string',
          enum: ['DEBUG', 'INFO', 'WARN', 'ERROR', 'FATAL'],
          description: 'Log level'
        },
        message: {
          bsonType: 'string',
          description: 'Log message'
        },
        timestamp: {
          bsonType: 'date',
          description: 'Log timestamp'
        },
        userId: {
          bsonType: 'string',
          description: 'User ID if applicable'
        },
        requestId: {
          bsonType: 'string',
          description: 'Request ID for tracing'
        },
        metadata: {
          bsonType: 'object',
          description: 'Additional log metadata'
        }
      }
    }
  }
});

// Create indexes for performance
db.files.createIndex({ 'uploadedBy': 1 });
db.files.createIndex({ 'uploadedAt': -1 });
db.files.createIndex({ 'contentType': 1 });
db.files.createIndex({ 'tags': 1 });

db.driver_documents.createIndex({ 'driverId': 1 });
db.driver_documents.createIndex({ 'documentType': 1 });
db.driver_documents.createIndex({ 'status': 1 });
db.driver_documents.createIndex({ 'expiryDate': 1 });
db.driver_documents.createIndex({ 'submittedAt': -1 });

db.vehicle_documents.createIndex({ 'vehicleId': 1 });
db.vehicle_documents.createIndex({ 'documentType': 1 });
db.vehicle_documents.createIndex({ 'status': 1 });
db.vehicle_documents.createIndex({ 'expiryDate': 1 });
db.vehicle_documents.createIndex({ 'submittedAt': -1 });

db.system_logs.createIndex({ 'service': 1 });
db.system_logs.createIndex({ 'level': 1 });
db.system_logs.createIndex({ 'timestamp': -1 });
db.system_logs.createIndex({ 'userId': 1 });
db.system_logs.createIndex({ 'requestId': 1 });

// Create TTL index for system logs (keep for 30 days)
db.system_logs.createIndex({ 'timestamp': 1 }, { expireAfterSeconds: 2592000 });

print('MongoDB initialization completed successfully!');
print('Created database: tecnodrive_documents');
print('Created collections: files, driver_documents, vehicle_documents, system_logs');
print('Created indexes for optimal performance');
print('Set up TTL for system logs (30 days retention)');
