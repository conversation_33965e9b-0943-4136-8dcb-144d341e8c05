-- TECNO DRIVE Database Initialization Script
-- Creates all required databases for microservices

-- Create databases for each service
CREATE DATABASE tecnodrive_auth;
CREATE DATABASE tecnodrive_users;
CREATE DATABASE tecnodrive_payments;
CREATE DATABASE tecnodrive_rides;
CREATE DATABASE tecnodrive_fleet;
CREATE DATABASE tecnodrive_locations;
CREATE DATABASE tecnodrive_analytics;
CREATE DATABASE tecnodrive_notifications;
CREATE DATABASE tecnodrive_parcels;

-- Create TimescaleDB extension for time-series data
\c tecnodrive_locations;
CREATE EXTENSION IF NOT EXISTS timescaledb;
CREATE EXTENSION IF NOT EXISTS postgis;

\c tecnodrive_analytics;
CREATE EXTENSION IF NOT EXISTS timescaledb;

-- Create users for each service (optional, for better security)
CREATE USER auth_service WITH PASSWORD 'auth_pass_2024';
CREATE USER user_service WITH PASSWORD 'user_pass_2024';
CREATE USER payment_service WITH PASSWORD 'payment_pass_2024';
CREATE USER ride_service WITH PASSWORD 'ride_pass_2024';
CREATE USER fleet_service WITH PASSWORD 'fleet_pass_2024';
CREATE USER location_service WITH PASSWORD 'location_pass_2024';
CREATE USER analytics_service WITH PASSWORD 'analytics_pass_2024';
CREATE USER notification_service WITH PASSWORD 'notification_pass_2024';
CREATE USER parcel_service WITH PASSWORD 'parcel_pass_2024';

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE tecnodrive_auth TO auth_service;
GRANT ALL PRIVILEGES ON DATABASE tecnodrive_users TO user_service;
GRANT ALL PRIVILEGES ON DATABASE tecnodrive_payments TO payment_service;
GRANT ALL PRIVILEGES ON DATABASE tecnodrive_rides TO ride_service;
GRANT ALL PRIVILEGES ON DATABASE tecnodrive_fleet TO fleet_service;
GRANT ALL PRIVILEGES ON DATABASE tecnodrive_locations TO location_service;
GRANT ALL PRIVILEGES ON DATABASE tecnodrive_analytics TO analytics_service;
GRANT ALL PRIVILEGES ON DATABASE tecnodrive_notifications TO notification_service;
GRANT ALL PRIVILEGES ON DATABASE tecnodrive_parcels TO parcel_service;

-- Create shared schemas for cross-service data
\c tecnodrive_locations;
CREATE SCHEMA IF NOT EXISTS shared;
CREATE TABLE IF NOT EXISTS shared.service_health (
    service_name VARCHAR(50) PRIMARY KEY,
    status VARCHAR(20) NOT NULL,
    last_check TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB
);

-- Insert initial health check data
INSERT INTO shared.service_health (service_name, status) VALUES
('auth-service', 'healthy'),
('user-service', 'healthy'),
('payment-service', 'healthy'),
('ride-service', 'healthy'),
('fleet-service', 'healthy'),
('location-service', 'healthy'),
('analytics-service', 'healthy'),
('notification-service', 'healthy'),
('parcel-service', 'healthy')
ON CONFLICT (service_name) DO NOTHING;
