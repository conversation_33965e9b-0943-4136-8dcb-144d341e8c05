server:
  port: 8081
  servlet:
    context-path: /auth-service

spring:
  application:
    name: auth-service
  
  profiles:
    active: dev
  
  datasource:
    url: ************************************************
    username: admin
    password: secret
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      leak-detection-threshold: 60000
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
  
  redis:
    host: localhost
    port: 6379
    password: 
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms
  
  security:
    oauth2:
      client:
        registration:
          google:
            client-id: ${GOOGLE_CLIENT_ID:your-google-client-id}
            client-secret: ${GOOGLE_CLIENT_SECRET:your-google-client-secret}
            scope: openid,profile,email
          facebook:
            client-id: ${FACEBOOK_CLIENT_ID:your-facebook-client-id}
            client-secret: ${FACEBOOK_CLIENT_SECRET:your-facebook-client-secret}
            scope: email,public_profile

eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
    register-with-eureka: true
    fetch-registry: true
  instance:
    prefer-ip-address: true
    lease-renewal-interval-in-seconds: 30
    lease-expiration-duration-in-seconds: 90

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

logging:
  level:
    com.tecnodrive.auth: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/auth-service.log

# JWT Configuration
jwt:
  secret: ${JWT_SECRET:your-super-secret-jwt-key-that-should-be-at-least-256-bits-long}
  access-token-expiration: 3600 # 1 hour in seconds
  refresh-token-expiration: 604800 # 7 days in seconds
  issuer: tecnodrive-auth-service

# Application specific configuration
app:
  security:
    max-login-attempts: 5
    account-lock-duration-minutes: 30
    password-reset-token-expiration-minutes: 15
    email-verification-token-expiration-hours: 24
  
  cors:
    allowed-origins: 
      - http://localhost:3000
      - http://localhost:3001
      - http://localhost:3002
      - http://localhost:4200
    allowed-methods: GET,POST,PUT,DELETE,OPTIONS
    allowed-headers: "*"
    allow-credentials: true

# OpenAPI Documentation
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
  info:
    title: TECNO DRIVE Auth Service API
    description: Authentication and Authorization Service for TECNO DRIVE Platform
    version: 1.0.0
    contact:
      name: TECNO DRIVE Team
      email: <EMAIL>

---
spring:
  config:
    activate:
      on-profile: docker
  
  datasource:
    url: ***********************************************
  
  redis:
    host: redis

eureka:
  client:
    service-url:
      defaultZone: http://eureka-server:8761/eureka/

---
spring:
  config:
    activate:
      on-profile: test
  
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    database-platform: org.hibernate.dialect.H2Dialect
  
  redis:
    host: localhost
    port: 6370 # Different port for testing
