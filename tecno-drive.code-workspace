{"folders": [{"path": "backend/microservices/core"}, {"path": "backend/microservices/business"}, {"path": "backend/microservices/infrastructure"}, {"path": "frontend/admin-dashboard"}, {"path": "frontend/driver-app"}, {"path": "frontend/passenger-app"}, {"path": "frontend/operator-dashboard"}, {"path": "database"}, {"path": "infrastructure"}, {"path": "tools"}], "settings": {"git.enableSmartCommit": true, "editor.formatOnSave": true, "terminal.integrated.profiles.windows": {"PowerShell": {"source": "PowerShell", "icon": "terminal-powershell"}, "Command Prompt": {"path": ["${env:windir}\\Sysnative\\cmd.exe", "${env:windir}\\System32\\cmd.exe"], "args": [], "icon": "terminal-cmd"}}, "terminal.integrated.profiles.linux": {"bash": {"path": "/bin/bash"}}, "terminal.integrated.defaultProfile.windows": "PowerShell", "extensions.recommendations": ["GitHub.copilot", "GitHub.copilot-chat", "hashicorp.terraform", "ms-vscode.cpptools", "ms-kubernetes-tools.vscode-kubernetes-tools", "ms-azuretools.vscode-docker", "eamodio.gitlens", "ms-vscode.vscode-json", "redhat.vscode-yaml", "ms-python.python", "vscjava.vscode-java-pack"]}}