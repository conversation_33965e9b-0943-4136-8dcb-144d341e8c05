{"version": "0.2.0", "configurations": [{"name": "Debug Spring Boot - API Gateway", "type": "java", "request": "launch", "mainClass": "com.tecnodrive.gateway.ApiGatewayApplication", "projectName": "api-gateway", "cwd": "${workspaceFolder}/backend/microservices/infrastructure/api-gateway", "env": {"SPRING_PROFILES_ACTIVE": "dev"}, "args": "", "vmArgs": "-Dspring.profiles.active=dev"}, {"name": "Debug Spring Boot - Ride Service", "type": "java", "request": "launch", "mainClass": "com.tecnodrive.ride.RideServiceApplication", "projectName": "ride-service", "cwd": "${workspaceFolder}/backend/microservices/business/ride-service", "env": {"SPRING_PROFILES_ACTIVE": "dev"}}, {"name": "Debug Spring Boot - User Service", "type": "java", "request": "launch", "mainClass": "com.tecnodrive.user.UserServiceApplication", "projectName": "user-service", "cwd": "${workspaceFolder}/backend/microservices/core/user-service", "env": {"SPRING_PROFILES_ACTIVE": "dev"}}, {"name": "Debug React - Admin Dashboard", "type": "chrome", "request": "launch", "url": "http://localhost:3000", "webRoot": "${workspaceFolder}/frontend/admin-dashboard/src", "sourceMaps": true, "userDataDir": "${workspaceFolder}/.vscode/chrome-debug-profile"}, {"name": "Debug React - Driver App", "type": "chrome", "request": "launch", "url": "http://localhost:3001", "webRoot": "${workspaceFolder}/frontend/driver-app/src", "sourceMaps": true, "userDataDir": "${workspaceFolder}/.vscode/chrome-debug-profile-driver"}, {"name": "Debug React - Passenger App", "type": "chrome", "request": "launch", "url": "http://localhost:3002", "webRoot": "${workspaceFolder}/frontend/passenger-app/src", "sourceMaps": true, "userDataDir": "${workspaceFolder}/.vscode/chrome-debug-profile-passenger"}, {"name": "Attach to Docker Container", "type": "java", "request": "attach", "hostName": "localhost", "port": 5005, "timeout": 30000}], "compounds": [{"name": "Debug Full Stack", "configurations": ["Debug Spring Boot - API Gateway", "Debug React - Admin Dashboard"]}]}