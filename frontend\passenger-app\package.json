{"name": "tecnodrive-passenger-app", "version": "1.0.0", "description": "TECNO DRIVE Passenger Mobile Application", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build": "expo build", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx"}, "dependencies": {"@expo/vector-icons": "^13.0.0", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/bottom-tabs": "^6.5.11", "expo": "~49.0.15", "expo-location": "~16.1.0", "expo-camera": "~13.4.4", "expo-notifications": "~0.20.1", "react": "18.2.0", "react-native": "0.72.6", "react-native-maps": "1.7.1", "react-native-gesture-handler": "~2.12.0", "react-native-reanimated": "~3.3.0", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-vector-icons": "^10.0.0", "axios": "^1.6.0", "react-query": "^3.39.3", "react-hook-form": "^7.47.0", "react-native-async-storage": "^1.19.5"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "@types/react-native": "~0.72.2", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "eslint": "^8.50.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-native": "^4.1.0", "jest": "^29.2.1", "typescript": "^5.1.3"}, "keywords": ["react-native", "expo", "passenger", "ride-sharing", "tecnodrive"], "author": "TECNO DRIVE Team", "license": "MIT", "private": true}