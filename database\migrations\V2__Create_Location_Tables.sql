-- Location Service Database Schema
-- Migration V2: Create location and tracking tables with PostGIS and TimescaleDB

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS postgis;
CREATE EXTENSION IF NOT EXISTS timescaledb;

-- Locations table for storing geographic points
CREATE TABLE locations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255),
    address TEXT,
    city VARCHAR(100),
    country VARCHAR(100),
    postal_code VARCHAR(20),
    coordinates GEOMETRY(POINT, 4326) NOT NULL,
    location_type VARCHAR(50) NOT NULL, -- 'pickup', 'dropoff', 'landmark', 'depot'
    metadata JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Vehicle tracking table (time-series data)
CREATE TABLE vehicle_tracking (
    id UUID DEFAULT gen_random_uuid(),
    vehicle_id UUID NOT NULL,
    driver_id UUID,
    coordinates GEOMETRY(POINT, 4326) NOT NULL,
    speed DECIMAL(5,2), -- km/h
    heading DECIMAL(5,2), -- degrees
    altitude DECIMAL(8,2), -- meters
    accuracy DECIMAL(8,2), -- meters
    battery_level INTEGER, -- percentage
    is_online BOOLEAN NOT NULL DEFAULT TRUE,
    event_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Convert to hypertable for time-series optimization
SELECT create_hypertable('vehicle_tracking', 'event_time');

-- Add dimension for vehicle_id to improve query performance
SELECT add_dimension('vehicle_tracking', 'vehicle_id', number_partitions => 10);

-- Routes table
CREATE TABLE routes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255),
    start_location_id UUID REFERENCES locations(id),
    end_location_id UUID REFERENCES locations(id),
    waypoints GEOMETRY(LINESTRING, 4326),
    distance_km DECIMAL(10,2),
    estimated_duration_minutes INTEGER,
    route_type VARCHAR(50), -- 'fastest', 'shortest', 'eco'
    traffic_data JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Geofences table for defining geographic boundaries
CREATE TABLE geofences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    boundary GEOMETRY(POLYGON, 4326) NOT NULL,
    fence_type VARCHAR(50) NOT NULL, -- 'service_area', 'restricted', 'depot'
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    metadata JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Geofence events table (time-series)
CREATE TABLE geofence_events (
    id UUID DEFAULT gen_random_uuid(),
    geofence_id UUID NOT NULL REFERENCES geofences(id),
    vehicle_id UUID NOT NULL,
    driver_id UUID,
    event_type VARCHAR(20) NOT NULL, -- 'enter', 'exit'
    coordinates GEOMETRY(POINT, 4326) NOT NULL,
    event_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Convert to hypertable
SELECT create_hypertable('geofence_events', 'event_time');

-- Traffic data table (time-series)
CREATE TABLE traffic_data (
    id UUID DEFAULT gen_random_uuid(),
    road_segment_id VARCHAR(100) NOT NULL,
    coordinates GEOMETRY(LINESTRING, 4326) NOT NULL,
    traffic_level INTEGER NOT NULL, -- 1-5 scale
    average_speed DECIMAL(5,2),
    congestion_factor DECIMAL(3,2),
    incident_reported BOOLEAN DEFAULT FALSE,
    weather_condition VARCHAR(50),
    event_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Convert to hypertable
SELECT create_hypertable('traffic_data', 'event_time');

-- Create spatial indexes for performance
CREATE INDEX idx_locations_coordinates ON locations USING GIST (coordinates);
CREATE INDEX idx_locations_type ON locations(location_type);
CREATE INDEX idx_locations_city ON locations(city);

CREATE INDEX idx_vehicle_tracking_coordinates ON vehicle_tracking USING GIST (coordinates);
CREATE INDEX idx_vehicle_tracking_vehicle_id ON vehicle_tracking(vehicle_id);
CREATE INDEX idx_vehicle_tracking_driver_id ON vehicle_tracking(driver_id);
CREATE INDEX idx_vehicle_tracking_event_time ON vehicle_tracking(event_time);

CREATE INDEX idx_routes_start_location ON routes(start_location_id);
CREATE INDEX idx_routes_end_location ON routes(end_location_id);
CREATE INDEX idx_routes_waypoints ON routes USING GIST (waypoints);

CREATE INDEX idx_geofences_boundary ON geofences USING GIST (boundary);
CREATE INDEX idx_geofences_type ON geofences(fence_type);
CREATE INDEX idx_geofences_active ON geofences(is_active);

CREATE INDEX idx_geofence_events_geofence_id ON geofence_events(geofence_id);
CREATE INDEX idx_geofence_events_vehicle_id ON geofence_events(vehicle_id);
CREATE INDEX idx_geofence_events_event_time ON geofence_events(event_time);

CREATE INDEX idx_traffic_data_road_segment ON traffic_data(road_segment_id);
CREATE INDEX idx_traffic_data_coordinates ON traffic_data USING GIST (coordinates);
CREATE INDEX idx_traffic_data_event_time ON traffic_data(event_time);

-- Create triggers for updated_at
CREATE TRIGGER update_locations_updated_at 
    BEFORE UPDATE ON locations 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_routes_updated_at 
    BEFORE UPDATE ON routes 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_geofences_updated_at 
    BEFORE UPDATE ON geofences 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create retention policies for time-series data
-- Keep vehicle tracking data for 1 year
SELECT add_retention_policy('vehicle_tracking', INTERVAL '1 year');

-- Keep geofence events for 6 months
SELECT add_retention_policy('geofence_events', INTERVAL '6 months');

-- Keep traffic data for 3 months
SELECT add_retention_policy('traffic_data', INTERVAL '3 months');
