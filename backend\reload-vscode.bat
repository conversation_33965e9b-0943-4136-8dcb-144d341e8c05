@echo off
echo ========================================
echo   VS Code Maven Configuration Fix
echo ========================================
echo.

echo [1/3] Force updating Maven dependencies...
mvn dependency:resolve -U -q

echo [2/3] Cleaning workspace cache...
if exist ".vscode\java_cache" rmdir /s /q ".vscode\java_cache"
if exist ".metadata" rmdir /s /q ".metadata"

echo [3/3] Recompiling all projects...
mvn compile -q

echo.
echo ========================================
echo   Configuration Updated Successfully!
echo ========================================
echo.
echo NEXT STEPS IN VS CODE:
echo.
echo 1. Press Ctrl+Shift+P and run:
echo    "Java: Reload Projects"
echo.
echo 2. If still showing errors, run:
echo    "Java: Clean Java Language Server Workspace"
echo.
echo 3. Restart VS Code if needed
echo.
echo The project is now ready for development!
echo.
pause
