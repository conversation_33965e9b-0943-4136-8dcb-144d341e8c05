{"java.configuration.updateBuildConfiguration": "automatic", "java.import.maven.enabled": true, "java.autobuild.enabled": true, "java.saveActions.organizeImports": true, "java.compile.nullAnalysis.mode": "automatic", "maven.executable.path": "mvn", "java.configuration.maven.userSettings": null, "java.project.sourcePaths": [], "java.project.referencedLibraries": [], "files.exclude": {"**/.classpath": true, "**/.project": true, "**/.settings": true, "**/.factorypath": true, "**/target": true, "**/*.iml": true, "**/*.ipr": true, "**/*.iws": true}, "java.configuration.runtimes": [], "maven.view": "hierarchical"}