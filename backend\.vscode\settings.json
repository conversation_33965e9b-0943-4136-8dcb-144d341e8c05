{"java.configuration.updateBuildConfiguration": "automatic", "java.import.maven.enabled": true, "java.autobuild.enabled": true, "java.saveActions.organizeImports": true, "java.compile.nullAnalysis.mode": "automatic", "maven.executable.path": "mvn", "java.configuration.maven.userSettings": null, "java.project.sourcePaths": [], "java.project.referencedLibraries": [], "java.configuration.workspaceCacheLimit": 90, "java.import.maven.offline.enabled": false, "java.maven.downloadSources": true, "java.maven.downloadJavadoc": false, "java.configuration.maven.globalSettings": null, "files.exclude": {"**/.classpath": true, "**/.project": true, "**/.settings": true, "**/.factorypath": true, "**/target": true, "**/*.iml": true, "**/*.ipr": true, "**/*.iws": true}, "java.configuration.runtimes": [], "maven.view": "hierarchical", "java.clean.workspace": true}