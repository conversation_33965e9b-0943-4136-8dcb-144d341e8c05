#!/bin/bash

echo "🚀 Setting up TECNO DRIVE development environment..."

# Install global npm packages
echo "📦 Installing global npm packages..."
npm install -g @angular/cli create-react-app typescript ts-node

# Install Python packages
echo "🐍 Installing Python packages..."
pip3 install --user pytest black flake8 mypy

# Set up Git configuration (if not already set)
if [ -z "$(git config --global user.name)" ]; then
    echo "⚙️ Setting up Git configuration..."
    git config --global user.name "TECNO DRIVE Developer"
    git config --global user.email "<EMAIL>"
fi

# Create project structure if it doesn't exist
echo "📁 Creating project structure..."
mkdir -p backend/microservices/{core,business,infrastructure}
mkdir -p frontend/{admin-dashboard,driver-app,passenger-app,operator-dashboard}
mkdir -p database/{migrations,seeds,backups}
mkdir -p infrastructure/{terraform,kubernetes,docker}
mkdir -p tools/{scripts,monitoring,testing}

# Create placeholder files
echo "📄 Creating placeholder files..."
touch backend/microservices/core/.gitkeep
touch backend/microservices/business/.gitkeep
touch backend/microservices/infrastructure/.gitkeep
touch frontend/admin-dashboard/.gitkeep
touch frontend/driver-app/.gitkeep
touch frontend/passenger-app/.gitkeep
touch frontend/operator-dashboard/.gitkeep
touch database/.gitkeep
touch infrastructure/.gitkeep
touch tools/.gitkeep

# Set permissions
chmod +x tools/scripts/*.sh 2>/dev/null || true

echo "✅ Development environment setup complete!"
echo "🎯 You can now start developing TECNO DRIVE!"
echo ""
echo "Next steps:"
echo "1. Run 'docker-compose up' to start services"
echo "2. Use Ctrl+Shift+P -> 'Tasks: Run Task' to build and start applications"
echo "3. Open the workspace file: tecno-drive.code-workspace"
