package com.tecnodrive.ride;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;

/**
 * TECNO DRIVE - Ride Service
 * 
 * This service handles:
 * - Ride request management and booking
 * - Driver-passenger matching
 * - Real-time ride tracking
 * - Cost calculation and distance management
 * 
 * <AUTHOR> DRIVE Team
 * @version 1.0.0
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableJpaAuditing
public class RideServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(RideServiceApplication.class, args);
    }
}
