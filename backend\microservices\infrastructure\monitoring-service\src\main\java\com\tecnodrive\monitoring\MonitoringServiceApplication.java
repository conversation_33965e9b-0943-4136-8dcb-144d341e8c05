package com.tecnodrive.monitoring;

import de.codecentric.boot.admin.server.config.EnableAdminServer;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * TECNO DRIVE - Monitoring Service
 * 
 * System monitoring and health management:
 * - Service health monitoring
 * - Performance metrics collection
 * - System alerts and notifications
 * - Dashboard for system overview
 * 
 * <AUTHOR> DRIVE Team
 * @version 1.0.0
 */
@SpringBootApplication
@EnableAdminServer
@EnableDiscoveryClient
public class MonitoringServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(MonitoringServiceApplication.class, args);
    }
}
