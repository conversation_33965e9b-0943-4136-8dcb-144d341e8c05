# 🚗 TECNO DRIVE - Smart Transportation Platform

TECNO DRIVE is a comprehensive ride-sharing and transportation management platform built with modern microservices architecture.

## 🏗️ Architecture Overview

```
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│   Admin Panel   │  │   Driver App    │  │ Passenger App   │
│    (React)      │  │    (React)      │  │    (React)      │
└─────────────────┘  └─────────────────┘  └─────────────────┘
         │                     │                     │
         └─────────────────────┼─────────────────────┘
                               │
                    ┌─────────────────┐
                    │   API Gateway   │
                    │  (Spring Boot)  │
                    └─────────────────┘
                               │
         ┌─────────────────────┼─────────────────────┐
         │                     │                     │
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│  User Service   │  │  Ride Service   │  │Payment Service  │
│ (Spring Boot)   │  │ (Spring Boot)   │  │ (Spring Boot)   │
└─────────────────┘  └─────────────────┘  └─────────────────┘
         │                     │                     │
         └─────────────────────┼─────────────────────┘
                               │
                    ┌─────────────────┐
                    │   PostgreSQL    │
                    │     Redis       │
                    │   RabbitMQ      │
                    └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites
- Docker & Docker Compose
- Node.js 18+
- Java 17+
- Maven 3.8+
- Git

### 1. Clone and Setup
```bash
git clone <repository-url>
cd newtech
```

### Current Implementation Status

✅ **Completed Services:**

**Core Services:**
- `auth-service` (Port 8081) - Authentication & Authorization with JWT + OAuth2
- `user-service` (Port 8082) - User Management & Profiles
- `payment-service` (Port 8085) - Payment Processing & Digital Wallets

**Infrastructure Services:**
- `eureka-server` (Port 8761) - Service Discovery
- `api-gateway` (Port 8080) - Central API Gateway with Rate Limiting
- `config-server` (Port 8888) - Centralized Configuration
- `monitoring-service` (Port 9090) - System Monitoring & Health Checks

🔄 **In Progress:**
- Business Services (ride, fleet, location, analytics, notification, parcel)
- Frontend Applications
- Database Integration
- AI/ML Integration

### 2. Open in VSCode
```bash
code tecno-drive.code-workspace
```

### 3. Start Development Environment
```bash
# Start infrastructure services
docker-compose -f docker-compose.dev.yml up -d

# Build and start microservices
mvn -f backend/pom.xml clean install
docker-compose up --build
```

### 4. Start Frontend Applications
```bash
# Admin Dashboard
cd frontend/admin-dashboard && yarn install && yarn start

# Driver App
cd frontend/driver-app && yarn install && yarn start

# Passenger App
cd frontend/passenger-app && yarn install && yarn start
```

## 🛠️ Development Tools

### VSCode Tasks
- **Ctrl+Shift+P** → "Tasks: Run Task"
- `Build All Microservices` - Build backend services
- `Start Backend Services` - Start all services with Docker
- `Start Admin Dashboard` - Start React admin panel
- `Run Tests - Backend` - Execute backend tests

### Dev Container
For consistent development environment:
1. Install "Remote - Containers" extension
2. **F1** → "Remote-Containers: Reopen in Container"
3. Wait for container setup to complete

### Database Access
- **PostgreSQL**: `localhost:5433` (dev) / `localhost:5432` (prod)
- **PgAdmin**: http://localhost:5050 (<EMAIL> / admin123)
- **Redis**: `localhost:6380` (dev) / `localhost:6379` (prod)
- **Redis Commander**: http://localhost:8081

### Monitoring
- **Grafana**: http://localhost:3000 (admin / admin)
- **Prometheus**: http://localhost:9090
- **RabbitMQ Management**: http://localhost:15673 (dev) / http://localhost:15672 (prod)

## 📁 Project Structure

```
tecno-drive/
├── backend/
│   └── microservices/
│       ├── core/                 # Core business services
│       │   ├── user-service/
│       │   └── auth-service/
│       ├── business/             # Business logic services
│       │   ├── ride-service/
│       │   ├── payment-service/
│       │   └── notification-service/
│       └── infrastructure/       # Infrastructure services
│           ├── api-gateway/
│           ├── eureka-server/
│           └── config-server/
├── frontend/
│   ├── admin-dashboard/          # React admin panel
│   ├── driver-app/              # React driver application
│   ├── passenger-app/           # React passenger application
│   └── operator-dashboard/      # React operator dashboard
├── database/
│   ├── migrations/              # Database migrations
│   ├── seeds/                   # Test data
│   └── backups/                 # Database backups
├── infrastructure/
│   ├── terraform/               # Infrastructure as Code
│   ├── kubernetes/              # K8s manifests
│   └── monitoring/              # Monitoring configs
└── tools/
    ├── scripts/                 # Utility scripts
    └── testing/                 # Testing utilities
```

## 🔧 Configuration

### Environment Variables
Create `.env` files in each service directory:

```bash
# Backend services
SPRING_PROFILES_ACTIVE=dev
DATABASE_URL=***********************************************
REDIS_URL=redis://localhost:6380
RABBITMQ_URL=amqp://dev:dev123@localhost:5673

# Frontend applications
REACT_APP_API_URL=http://localhost:8080
REACT_APP_ENVIRONMENT=development
```

## 🧪 Testing

### Backend Tests
```bash
# Run all tests
mvn test

# Run specific service tests
mvn -f backend/microservices/core/user-service/pom.xml test

# Integration tests
mvn verify -Pintegration-tests
```

### Frontend Tests
```bash
# Admin Dashboard
cd frontend/admin-dashboard && yarn test

# Driver App
cd frontend/driver-app && yarn test

# E2E Tests
cd tools/testing && yarn cypress:run
```

## 📦 Deployment

### Docker Production
```bash
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

### Kubernetes
```bash
kubectl apply -f infrastructure/kubernetes/
```

## 🤖 AI Integration

### GitHub Copilot
- Enabled for code completion and chat
- Use **Ctrl+I** for inline suggestions
- Use **Ctrl+Shift+I** for Copilot Chat

### Recommended Extensions
- GitHub Copilot
- GitHub Copilot Chat
- Docker
- Kubernetes
- GitLens
- Java Extension Pack
- ES7+ React/Redux/React-Native snippets

## 🔍 Troubleshooting

### Common Issues

1. **Port conflicts**: Check if ports 3000, 8080, 5432 are available
2. **Docker issues**: Run `docker system prune` to clean up
3. **Java version**: Ensure Java 17 is installed and JAVA_HOME is set
4. **Node version**: Use Node.js 18+ for frontend applications

### Logs
```bash
# View service logs
docker-compose logs -f api-gateway
docker-compose logs -f user-service

# View all logs
docker-compose logs -f
```

## 📚 Documentation

- [API Documentation](./docs/api.md)
- [Database Schema](./docs/database.md)
- [Deployment Guide](./docs/deployment.md)
- [Contributing Guidelines](./docs/contributing.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

**Happy Coding! 🚀**
