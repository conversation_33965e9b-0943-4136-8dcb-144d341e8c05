package com.tecnodrive.auth.controller;

import com.tecnodrive.auth.dto.*;
import com.tecnodrive.auth.service.AuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

/**
 * Authentication Controller
 * Handles authentication and authorization endpoints
 */
@RestController
@RequestMapping("/api/v1/auth")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Authentication", description = "Authentication and authorization endpoints")
public class AuthController {

    private final AuthService authService;

    @Operation(summary = "Register new user", description = "Register a new user account")
    @PostMapping("/register")
    public ResponseEntity<LoginResponse> register(
            @Valid @RequestBody RegisterRequest request,
            HttpServletRequest httpRequest) {
        
        // Set device info and IP address
        request.setDeviceInfo(extractDeviceInfo(httpRequest));
        request.setIpAddress(extractClientIpAddress(httpRequest));
        
        LoginResponse response = authService.register(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @Operation(summary = "User login", description = "Authenticate user and return JWT tokens")
    @PostMapping("/login")
    public ResponseEntity<LoginResponse> login(
            @Valid @RequestBody LoginRequest request,
            HttpServletRequest httpRequest) {
        
        // Set device info and IP address
        request.setDeviceInfo(extractDeviceInfo(httpRequest));
        request.setIpAddress(extractClientIpAddress(httpRequest));
        
        LoginResponse response = authService.login(request);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Refresh access token", description = "Get new access token using refresh token")
    @PostMapping("/refresh")
    public ResponseEntity<LoginResponse> refreshToken(
            @Valid @RequestBody TokenRefreshRequest request,
            HttpServletRequest httpRequest) {
        
        // Set device info and IP address
        request.setDeviceInfo(extractDeviceInfo(httpRequest));
        request.setIpAddress(extractClientIpAddress(httpRequest));
        
        LoginResponse response = authService.refreshToken(request);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Logout", description = "Logout user and invalidate refresh token")

    @PostMapping("/logout")
    public ResponseEntity<AuthController.ApiResponse> logout(@RequestBody(required = false) LogoutRequest request) {
        String refreshToken = request != null ? request.getRefreshToken() : null;
        authService.logout(refreshToken);
        
        return ResponseEntity.ok(AuthController.ApiResponse.builder()
                .success(true)
                .message("Logout successful")
                .build());
    }

    @Operation(summary = "Logout from all devices", description = "Logout user from all devices")

    @PostMapping("/logout-all")
    public ResponseEntity<AuthController.ApiResponse> logoutFromAllDevices(@RequestParam UUID userId) {
        authService.logoutFromAllDevices(userId);
        
        return ResponseEntity.ok(AuthController.ApiResponse.builder()
                .success(true)
                .message("Logout from all devices successful")
                .build());
    }

    @Operation(summary = "Validate token", description = "Validate JWT access token")

    @PostMapping("/validate")
    public ResponseEntity<TokenValidationResponse> validateToken(
            @RequestBody TokenValidationRequest request) {
        
        // This endpoint will be implemented when we add JWT validation logic
        return ResponseEntity.ok(TokenValidationResponse.builder()
                .valid(true)
                .message("Token validation endpoint - to be implemented")
                .build());
    }

    /**
     * Extract device information from request headers
     */
    private String extractDeviceInfo(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");
        return userAgent != null ? userAgent : "Unknown Device";
    }

    /**
     * Extract client IP address from request
     */
    private String extractClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

    // Additional DTOs for controller
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class LogoutRequest {
        private String refreshToken;
    }

    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class ApiResponse {
        private boolean success;
        private String message;
        private Object data;
    }

    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class TokenValidationRequest {
        private String token;
    }

    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class TokenValidationResponse {
        private boolean valid;
        private String message;
        private Object userInfo;
    }
}
