version: '3.8'

services:
  # Database Services
  postgres:
    image: postgres:15-alpine
    container_name: tecnodrive-postgres
    environment:
      POSTGRES_DB: tecnodrive
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: secret
      POSTGRES_MULTIPLE_DATABASES: tecnodrive_users,tecnodrive_rides,tecnodrive_payments,tecnodrive_notifications
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - tecnodrive-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U admin -d tecnodrive"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Cache Service
  redis:
    image: redis:7-alpine
    container_name: tecnodrive-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./database/redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - tecnodrive-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MongoDB for Document Storage
  mongodb:
    image: mongo:7
    container_name: tecnodrive-mongodb
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: secret
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./database/mongodb/init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js
    networks:
      - tecnodrive-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3

  # TimescaleDB for Time-Series Data
  timescaledb:
    image: timescale/timescaledb:latest-pg15
    container_name: tecnodrive-timescaledb
    environment:
      POSTGRES_DB: tecnodrive_timeseries
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: secret
    ports:
      - "5433:5432"
    volumes:
      - timescaledb_data:/var/lib/postgresql/data
      - ./database/migrations:/docker-entrypoint-initdb.d
    networks:
      - tecnodrive-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U admin -d tecnodrive_timeseries"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Message Queue
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: tecnodrive-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: secret
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - tecnodrive-network

  # Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: tecnodrive-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./infrastructure/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - tecnodrive-network

  grafana:
    image: grafana/grafana:latest
    container_name: tecnodrive-grafana
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./infrastructure/monitoring/grafana:/etc/grafana/provisioning
    networks:
      - tecnodrive-network

  # Microservices (will be built from source)
  api-gateway:
    build:
      context: ./backend/microservices/infrastructure/api-gateway
      dockerfile: Dockerfile
    container_name: tecnodrive-api-gateway
    ports:
      - "8080:8080"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE: http://eureka-server:8761/eureka
      SPRING_DATASOURCE_URL: ******************************************
      SPRING_REDIS_HOST: redis
      SPRING_RABBITMQ_HOST: rabbitmq
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      eureka-server:
        condition: service_started
    networks:
      - tecnodrive-network

  eureka-server:
    build:
      context: ./backend/microservices/infrastructure/eureka-server
      dockerfile: Dockerfile
    container_name: tecnodrive-eureka
    ports:
      - "8761:8761"
    environment:
      SPRING_PROFILES_ACTIVE: docker
    networks:
      - tecnodrive-network

  user-service:
    build:
      context: ./backend/microservices/core/user-service
      dockerfile: Dockerfile
    container_name: tecnodrive-user-service
    ports:
      - "8081:8081"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE: http://eureka-server:8761/eureka
      SPRING_DATASOURCE_URL: ******************************************_users
      SPRING_REDIS_HOST: redis
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      eureka-server:
        condition: service_started
    networks:
      - tecnodrive-network

  ride-service:
    build:
      context: ./backend/microservices/business/ride-service
      dockerfile: Dockerfile
    container_name: tecnodrive-ride-service
    ports:
      - "8082:8082"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE: http://eureka-server:8761/eureka
      SPRING_DATASOURCE_URL: ******************************************_rides
      SPRING_REDIS_HOST: redis
      SPRING_RABBITMQ_HOST: rabbitmq
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      eureka-server:
        condition: service_started
    networks:
      - tecnodrive-network

volumes:
  postgres_data:
  redis_data:
  mongodb_data:
  timescaledb_data:
  rabbitmq_data:
  prometheus_data:
  grafana_data:

networks:
  tecnodrive-network:
    driver: bridge
