package com.tecnodrive.analytics.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DemandPrediction {
    private String areaId;
    private Integer predictedDemand;
    private Double confidence;
    private String timeWindow;
    private Map<String, Object> factors;
    private LocalDateTime generatedAt;
}
