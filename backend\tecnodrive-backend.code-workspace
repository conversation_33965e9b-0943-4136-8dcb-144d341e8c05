{"folders": [{"name": "TECNO DRIVE Backend", "path": "."}], "settings": {"java.configuration.updateBuildConfiguration": "automatic", "java.import.maven.enabled": true, "java.autobuild.enabled": true, "maven.executable.path": "mvn", "java.compile.nullAnalysis.mode": "automatic", "java.configuration.maven.userSettings": null, "files.exclude": {"**/.classpath": true, "**/.project": true, "**/.settings": true, "**/.factorypath": true, "**/target": true, "**/*.iml": true, "**/*.ipr": true, "**/*.iws": true}}, "extensions": {"recommendations": ["vscjava.vscode-java-pack", "vscjava.vscode-maven", "redhat.java", "vscjava.vscode-java-dependency"]}}