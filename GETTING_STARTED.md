# 🚀 TECNO DRIVE - دليل البدء السريع

## ✅ الخدمات المكتملة

### الخدمات الأساسية (Core Services)
- **auth-service** (Port 8081) - المصادقة والترخيص مع JWT + OAuth2
- **user-service** (Port 8082) - إدارة المستخدمين والملفات الشخصية  
- **payment-service** (Port 8085) - معالجة المدفوعات والمحافظ الرقمية

### خدمات البنية التحتية (Infrastructure Services)
- **eureka-server** (Port 8761) - اكتشاف الخدمات
- **api-gateway** (Port 8080) - بوابة API المركزية مع تحديد المعدل
- **config-server** (Port 8888) - التكوين المركزي
- **monitoring-service** (Port 9090) - مراقبة النظام وفحص الصحة

## 🛠️ تشغيل النظام

### 1. تشغيل قواعد البيانات والخدمات الأساسية
```bash
docker-compose up -d postgres redis rabbitmq
```

### 2. بناء الخدمات المصغرة
```bash
mvn -f backend/pom.xml clean install -DskipTests
```

### 3. تشغيل خدمات البنية التحتية أولاً
```bash
# تشغيل Eureka Server
cd backend/microservices/infrastructure/eureka-server
mvn spring-boot:run

# في terminal جديد - تشغيل Config Server
cd backend/microservices/infrastructure/config-server
mvn spring-boot:run

# في terminal جديد - تشغيل API Gateway
cd backend/microservices/infrastructure/api-gateway
mvn spring-boot:run
```

### 4. تشغيل الخدمات الأساسية
```bash
# في terminal جديد - تشغيل Auth Service
cd backend/microservices/core/auth-service
mvn spring-boot:run

# في terminal جديد - تشغيل User Service
cd backend/microservices/core/user-service
mvn spring-boot:run

# في terminal جديد - تشغيل Payment Service
cd backend/microservices/core/payment-service
mvn spring-boot:run
```

## 🔗 نقاط الوصول

### خدمات النظام
- **API Gateway**: http://localhost:8080
- **Eureka Dashboard**: http://localhost:8761
- **Monitoring Dashboard**: http://localhost:9090

### قواعد البيانات والمراقبة
- **PostgreSQL**: localhost:5432 (admin/secret)
- **Redis**: localhost:6379
- **RabbitMQ Management**: http://localhost:15672 (admin/secret)
- **Grafana**: http://localhost:3000 (admin/admin)
- **Prometheus**: http://localhost:9090

### APIs المتاحة
- **Authentication**: http://localhost:8080/api/v1/auth/
  - POST `/register` - تسجيل مستخدم جديد
  - POST `/login` - تسجيل الدخول
  - POST `/refresh` - تجديد الرمز المميز
  - POST `/logout` - تسجيل الخروج

## 🧪 اختبار النظام

### 1. فحص صحة الخدمات
```bash
# فحص Eureka Server
curl http://localhost:8761/actuator/health

# فحص API Gateway
curl http://localhost:8080/actuator/health

# فحص Auth Service
curl http://localhost:8081/auth-service/actuator/health
```

### 2. اختبار تسجيل مستخدم جديد
```bash
curl -X POST http://localhost:8080/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Test123!@#",
    "firstName": "أحمد",
    "lastName": "محمد",
    "phoneNumber": "+966501234567"
  }'
```

### 3. اختبار تسجيل الدخول
```bash
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "<EMAIL>",
    "password": "Test123!@#"
  }'
```

## 📝 المتغيرات البيئية

انسخ `.env.example` إلى `.env` وحدث القيم:

```bash
cp .env.example .env
```

القيم المهمة:
- `JWT_SECRET`: مفتاح JWT (يجب أن يكون 256 بت على الأقل)
- `POSTGRES_PASSWORD`: كلمة مرور PostgreSQL
- `REDIS_PASSWORD`: كلمة مرور Redis (اختياري)

## 🐛 استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في الاتصال بقاعدة البيانات**
   ```bash
   # تأكد من تشغيل PostgreSQL
   docker-compose ps postgres
   ```

2. **خدمة غير مسجلة في Eureka**
   ```bash
   # تحقق من Eureka Dashboard
   open http://localhost:8761
   ```

3. **خطأ في API Gateway**
   ```bash
   # تحقق من logs
   docker-compose logs api-gateway
   ```

## 📚 الخطوات التالية

1. **إنشاء خدمات الأعمال** (ride, fleet, location, analytics, notification, parcel)
2. **تطوير الواجهات الأمامية** (React Admin Dashboard, React Native Apps)
3. **تكامل قواعد البيانات** (PostgreSQL, TimescaleDB, MongoDB)
4. **دمج الخرائط والذكاء الاصطناعي**
5. **إعداد CI/CD والنشر**

## 🆘 الحصول على المساعدة

- تحقق من logs الخدمات: `docker-compose logs [service-name]`
- راجع Eureka Dashboard للتأكد من تسجيل الخدمات
- استخدم Actuator endpoints للفحص الصحي: `/actuator/health`
