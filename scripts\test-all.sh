#!/bin/bash

# TECNO DRIVE - Simple Test Script
# Tests the basic functionality of the system

echo "🚗 TECNO DRIVE - System Test"
echo "============================"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Test database connections
echo "📊 Testing Database Connections..."

# Test PostgreSQL
echo -n "PostgreSQL: "
if docker exec tecnodrive-postgres pg_isready -U admin > /dev/null 2>&1; then
    echo -e "${GREEN}✓ Connected${NC}"
else
    echo -e "${RED}✗ Failed${NC}"
fi

# Test Redis
echo -n "Redis: "
if docker exec tecnodrive-redis redis-cli ping > /dev/null 2>&1; then
    echo -e "${GREEN}✓ Connected${NC}"
else
    echo -e "${RED}✗ Failed${NC}"
fi

# Test MongoDB
echo -n "MongoDB: "
if docker exec tecnodrive-mongodb mongosh --eval "db.adminCommand('ping')" > /dev/null 2>&1; then
    echo -e "${GREEN}✓ Connected${NC}"
else
    echo -e "${RED}✗ Failed${NC}"
fi

echo ""
echo "🔧 Testing Services..."

# Test if services are running
services=("eureka-server" "api-gateway" "auth-service")
for service in "${services[@]}"; do
    echo -n "$service: "
    if curl -s http://localhost:8761/eureka/apps | grep -q "$service" > /dev/null 2>&1; then
        echo -e "${GREEN}✓ Running${NC}"
    else
        echo -e "${YELLOW}⚠ Not registered${NC}"
    fi
done

echo ""
echo "🌐 Testing API Endpoints..."

# Test API Gateway
echo -n "API Gateway Health: "
if curl -s http://localhost:8080/actuator/health > /dev/null 2>&1; then
    echo -e "${GREEN}✓ Healthy${NC}"
else
    echo -e "${RED}✗ Unhealthy${NC}"
fi

echo ""
echo "✅ Test completed!"
echo ""
echo "📋 Next Steps:"
echo "1. Fix Maven dependency issues"
echo "2. Complete missing DTOs and entities"
echo "3. Run integration tests"
echo "4. Deploy to staging environment"
