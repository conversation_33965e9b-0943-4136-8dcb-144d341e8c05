{"name": "TECNO DRIVE DevContainer", "dockerFile": "Dockerfile", "workspaceFolder": "/workspace", "shutdownAction": "stopContainer", "mounts": ["source=${localWorkspaceFolder},target=/workspace,type=bind,consistency=cached"], "forwardPorts": [3000, 3001, 3002, 8080, 8000, 9090, 5432, 6379], "portsAttributes": {"3000": {"label": "Admin Dashboard", "onAutoForward": "notify"}, "3001": {"label": "Driver App", "onAutoForward": "notify"}, "3002": {"label": "Passenger App", "onAutoForward": "notify"}, "8080": {"label": "API Gateway", "onAutoForward": "notify"}, "5432": {"label": "PostgreSQL", "onAutoForward": "silent"}, "6379": {"label": "Redis", "onAutoForward": "silent"}}, "customizations": {"vscode": {"extensions": ["GitHub.copilot", "GitHub.copilot-chat", "hashicorp.terraform", "ms-vscode.cpptools", "ms-kubernetes-tools.vscode-kubernetes-tools", "ms-azuretools.vscode-docker", "eamodio.gitlens", "vscjava.vscode-java-pack", "ms-python.python", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "ms-vscode.vscode-json", "redhat.vscode-yaml", "ms-vscode.powershell"], "settings": {"terminal.integrated.defaultProfile.linux": "bash", "java.home": "/usr/lib/jvm/java-17-openjdk-amd64", "maven.executable.path": "/usr/bin/mvn", "git.enableSmartCommit": true, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": true}}}}, "postCreateCommand": "bash .devcontainer/post-create.sh", "remoteUser": "vscode", "features": {"ghcr.io/devcontainers/features/java:1": {"version": "17", "installMaven": true}, "ghcr.io/devcontainers/features/node:1": {"version": "18"}, "ghcr.io/devcontainers/features/python:1": {"version": "3.11"}, "ghcr.io/devcontainers/features/docker-in-docker:2": {}, "ghcr.io/devcontainers/features/kubectl-helm-minikube:1": {}}}