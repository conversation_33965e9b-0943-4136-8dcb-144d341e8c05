@echo off
echo ========================================
echo   TECNO DRIVE - Maven Config Fix
echo ========================================
echo.

echo [1/4] Cleaning all projects...
mvn clean -q
if %errorlevel% neq 0 (
    echo ERROR: Clean failed
    pause
    exit /b 1
)

echo [2/4] Installing dependencies...
mvn install -DskipTests -q
if %errorlevel% neq 0 (
    echo ERROR: Install failed
    pause
    exit /b 1
)

echo [3/4] Resolving dependencies...
mvn dependency:resolve -q
if %errorlevel% neq 0 (
    echo ERROR: Dependency resolve failed
    pause
    exit /b 1
)

echo [4/4] Generating Eclipse project files...
mvn eclipse:eclipse -q
if %errorlevel% neq 0 (
    echo WARNING: Eclipse project generation failed (normal if not using Eclipse)
)

echo.
echo ========================================
echo   SUCCESS: Maven configuration fixed!
echo ========================================
echo.
echo Next steps based on your IDE:
echo.
echo VS Code:
echo   - Press Ctrl+Shift+P
echo   - Type: Java: Reload Projects
echo.
echo Eclipse:
echo   - Right-click projects
echo   - Maven -^> Update Project
echo.
echo Other IDEs:
echo   - Look for "Reload Maven Projects" option
echo   - Or restart your IDE
echo.
pause
