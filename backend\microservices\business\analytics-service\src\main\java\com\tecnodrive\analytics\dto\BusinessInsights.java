package com.tecnodrive.analytics.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Business Insights DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BusinessInsights {
    private String period;
    private Map<String, Object> kpis;
    private List<Trend> trends;
    private List<Anomaly> anomalies;
    private List<Recommendation> recommendations;
    private double confidence;
    private LocalDateTime generatedAt;
}
