apiVersion: v1
kind: ConfigMap
metadata:
  name: tecnodrive-config
  namespace: tecnodrive
data:
  # Database Configuration
  POSTGRES_HOST: "postgres-service"
  POSTGRES_PORT: "5432"
  POSTGRES_DB: "tecnodrive"
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  MONGODB_HOST: "mongodb-service"
  MONGODB_PORT: "27017"
  
  # Service Discovery
  EUREKA_SERVER_URL: "http://eureka-service:8761/eureka/"
  
  # Application Configuration
  SPRING_PROFILES_ACTIVE: "production"
  LOG_LEVEL: "INFO"
  
  # JWT Configuration
  JWT_ISSUER: "tecnodrive-production"
  JWT_ACCESS_TOKEN_EXPIRATION: "3600"
  JWT_REFRESH_TOKEN_EXPIRATION: "604800"
  
  # CORS Configuration
  CORS_ORIGINS: "https://admin.tecnodrive.com,https://driver.tecnodrive.com,https://passenger.tecnodrive.com"
  
  # Rate Limiting
  RATE_LIMIT_WINDOW: "15"
  RATE_LIMIT_MAX_REQUESTS: "100"
  
  # File Upload
  MAX_FILE_SIZE: "10MB"
  UPLOAD_PATH: "/app/uploads"
  
  # Monitoring
  PROMETHEUS_PORT: "9090"
  GRAFANA_PORT: "3000"
  
  # AI/ML Configuration
  SPARK_MASTER_URL: "spark://spark-master:7077"
  ML_PREDICTION_THRESHOLD: "0.8"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: tecnodrive-config-staging
  namespace: tecnodrive-staging
data:
  # Database Configuration
  POSTGRES_HOST: "postgres-service"
  POSTGRES_PORT: "5432"
  POSTGRES_DB: "tecnodrive_staging"
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  MONGODB_HOST: "mongodb-service"
  MONGODB_PORT: "27017"
  
  # Service Discovery
  EUREKA_SERVER_URL: "http://eureka-service:8761/eureka/"
  
  # Application Configuration
  SPRING_PROFILES_ACTIVE: "staging"
  LOG_LEVEL: "DEBUG"
  
  # JWT Configuration
  JWT_ISSUER: "tecnodrive-staging"
  JWT_ACCESS_TOKEN_EXPIRATION: "3600"
  JWT_REFRESH_TOKEN_EXPIRATION: "604800"
  
  # CORS Configuration
  CORS_ORIGINS: "https://staging-admin.tecnodrive.com,https://staging-driver.tecnodrive.com,https://staging-passenger.tecnodrive.com"
  
  # Rate Limiting
  RATE_LIMIT_WINDOW: "15"
  RATE_LIMIT_MAX_REQUESTS: "1000"
  
  # File Upload
  MAX_FILE_SIZE: "10MB"
  UPLOAD_PATH: "/app/uploads"
  
  # Monitoring
  PROMETHEUS_PORT: "9090"
  GRAFANA_PORT: "3000"
  
  # AI/ML Configuration
  SPARK_MASTER_URL: "spark://spark-master:7077"
  ML_PREDICTION_THRESHOLD: "0.7"
